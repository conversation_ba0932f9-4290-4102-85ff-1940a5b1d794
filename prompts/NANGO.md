## GENERATING NANGO ACTIONS FOR INTEGRATIONS

### Context: What is Nango?
Nango is an open-source integration platform that simplifies building and managing API integrations. It provides a framework for creating **actions** (one-off API operations) and **syncs** (recurring data synchronization) with third-party APIs. Nango handles authentication, connection management, and schema validation, allowing developers to focus on integration logic. Integrations are defined via `nango.yaml` configuration files, which generate TypeScript models and action templates.

### Project Context
This project involves developing Nango integrations within two primary packages:
- **`packages/emcpe-nango-integrations`**: Contains **actions** for provider-specific integrations (e.g., Dropbox, Slack). Each provider has its own subdirectory (e.g., `packages/emcpe-nango-integrations/dropbox`) with a dedicated `nango.yaml` and action implementation files.
- **`packages/makeagent-nango-integrations`**: Contains **syncs** and some actions, with a single root `nango.yaml` and provider-specific directories (e.g., `packages/makeagent-nango-integrations/google-mail`).
- **Final Deployment**: Integrations from both packages are merged into `packages/nango-integrations` using `scripts/mergeNangoIntegrations.ts`, which creates a unified `nango.yaml` and copies implementation files. Deployment occurs from `packages/nango-integrations` via `npm run nango:deploy`.

This prompt focuses on **authoring new actions**, primarily in `emcpe-nango-integrations`, though the process is adaptable for `makeagent-nango-integrations`.

### General Process for Authoring Nango Actions
Follow these steps to create or update a Nango action:
1. **Modify Configuration**:
   - Edit the provider-specific `nango.yaml` in the relevant directory:
     - For `emcpe-nango-integrations`: Located in the provider subdirectory (e.g., `packages/emcpe-nango-integrations/dropbox/nango.yaml`).
     - For `makeagent-nango-integrations`: Located at the root (e.g., `packages/makeagent-nango-integrations/nango.yaml`).
   - Define or update the action (e.g., name, input/output models) and associated models to match the provider API's response structure.
   - Ensure models reflect the full API response shape, including nested fields, to avoid validation errors.
2. **Generate Files**:
   - Run `nango generate` from the provider directory (e.g., `cd packages/emcpe-nango-integrations/dropbox && nango generate`) or at the root for `makeagent-nango-integrations`.
   - This scaffolds or updates `models.ts` and action template files (e.g., `actions/[action-name].ts`).
3. **Implement Action Logic**:
   - Update the generated action file in the appropriate directory:
     - For `emcpe-nango-integrations`: `packages/emcpe-nango-integrations/[provider]/actions/[action-name].ts`.
     - For `makeagent-nango-integrations`: `packages/makeagent-nango-integrations/[provider]/actions/[action-name].ts`.
   - Use `nango.proxy` to call the provider API, ensuring the response matches the output model defined in `nango.yaml`.
   - Implement error handling as per the coding guidelines below.
4. **Test with Dry Run**:
   - Run `nango dryrun [action-name] -e prod [connection-id] --integration-id [provider-key] --input '{"param":"value"}'` from the provider directory (or root for `makeagent-nango-integrations`).
   - Obtain a valid `connection-id` by running `pnpm connections-e` from the project root. Prefer local connection IDs.
   - Ensure input parameters are valid and realistic (e.g., use existing resource IDs from the provider).
5. **Validate Output**:
   - Once the dry run produces valid output, repeat with the `--validation` flag: `nango dryrun [action-name] -e prod [connection-id] --integration-id [provider-key] --input '{"param":"value"}' --validation`.
   - Fix any schema mismatches by updating `nango.yaml` models or sanitizing the action response to match the defined schema.

---

### Mechanics of the Process
- **Directory Context**:
  - The terminal starts at the **project root** for each new session or command execution.
  - Commands like `nango generate` and `nango dryrun` must be run from the provider directory (e.g., `packages/emcpe-nango-integrations/dropbox`) for `emcpe-nango-integrations` or the root for `makeagent-nango-integrations`.
  - The terminal is **persistent** across commands within a session. After running `cd packages/emcpe-nango-integrations/dropbox`, subsequent commands (e.g., `nango generate`) can be run directly from that directory.
  - If unsure of the current directory, run `pwd` to confirm and adjust commands accordingly (e.g., prepend `cd` if needed).
- **Stay on Task**:
  - Focus strictly on the requested action development steps.
  - Avoid unrelated tasks (e.g., creating syncs, deploying to prod, generating test files).
  - Do not modify files or run commands outside the specified process unless explicitly instructed.

### Coding Guidelines for Actions
#### Code Standards
- **Language**: Use TypeScript for all action implementations.
- **File Structure**:
  - Actions are located in `actions/[action-name].ts` within the provider directory.
  - Do not edit `models.ts` directly; update `nango.yaml` and run `nango generate` to regenerate it.
- **Model Design**:
  - Define models in `nango.yaml` to mirror the provider API’s response structure, including all relevant fields and nested objects.
  - For `emcpe-nango-integrations`, include only models relevant to the provider’s actions in its `nango.yaml`.
  - Avoid cyclical dependencies in models. If Nango reports cyclical issues, create specific sub-models (e.g., `SubEntityAsChildOfEntity`) or use `object[]` for arrays of nested objects.
  - If a model references an undefined type, Nango will error (e.g., “A literal type 'TypeABC' was parsed”). Ensure all referenced types are defined in the `models` section of `nango.yaml`.
- **Action Implementation**:
  - Use `nango.proxy` for API calls, specifying the `endpoint` relative to the provider’s base URL (defined in Nango’s core `providers.yaml`). Do not rely on the `endpoint` field in `nango.yaml` for API paths; it’s primarily for display.
  - Return the full API response, ensuring it matches the output model defined in `nango.yaml`.
  - Remove unused imports after model changes to prevent TypeScript errors.
- **Error Handling**:
  - Wrap `nango.proxy` calls in a `try...catch` block.
  - Define a `NangoError` type: `{ error: { status: number; message: string } }` and include it in the action’s return type (e.g., `Promise<OutputType | NangoError>`).
  - In the `catch` block:
    - Extract the error message from `error.response?.data?.message` or `error.message`.
    - Determine the status code (default to 500 if unavailable).
    - Log the error using `await nango.log('Error: ' + message, { level: 'error' })`.
    - Return `{ error: { status, message } }`.
  - Example:
    ```typescript
    import type { OutputType } from '../../models';

    type NangoError = {
      error: {
        status: number;
        message: string;
      };
    };


    export default async function action(nango: Nango): Promise<OutputType | NangoError> {
      try {
        const response = await nango.proxy({ endpoint: '/api/endpoint' });
        return response.data;
      } catch (error: any) {
        const message = error.response?.data?.message || error.message || 'Unknown error';
        const status = error.response?.status || 500;
        await nango.log(`Error: ${message}`, { level: 'error' });
        return { error: { status, message } };
      }
    }
    ```
- **Metadata Usage**:
  - Use `nango.setMetadata` and `nango.getMetadata` to cache connection-specific data (e.g., account IDs).
  - Do not use metadata for state management between requests; it’s strictly for caching.
  - Example:
    ```typescript
    const accountId = await nango.getMetadata<string>('accountId');
    if (!accountId) {
      // Fetch account ID from API
      await nango.setMetadata('accountId', fetchedAccountId);
    }
    ```

#### Critical Coding Guidelines and Common Pitfalls

**Type Safety is Mandatory**
- Never use `Promise<any>` for action outputs. All actions must use fully typed outputs that match the models generated from `nango.yaml`.
- The express intention is to have full type safety at all times. If the provider response changes, update `nango.yaml` and regenerate types.

**Error Handling is Required**
- Never remove error handling. All actions must return a `NangoError` type for non-200 responses.
- Axios will throw for any non-2xx response. Always catch errors and return a `NangoError` object with the status and message.
- Do not rely on returning raw errors or letting exceptions propagate; always structure errors as per the `NangoError` type.

**Correct Workflow for Action Implementation**
1. Implement the action to return the full provider data (do not restrict or map output).
2. Run a dry run and validation. If the output does not match the model, update `nango.yaml` to match the real provider response.
3. Regenerate types with `nango generate`.
4. Update the action to use the correct, fully typed output and error handling.
5. Repeat dry run and validation until the output and model match exactly.

**What NOT to Do**
- Do not use `any` in function signatures or return types.
- Do not remove error handling or return untyped errors.
- Do not restrict the provider response to a subset of fields unless required for schema validation.
- Do not ignore validation errors; always update models to match the real API.

**Summary of Learnings**
- Always loop through actions, return the full provider data, validate, and update models/types as needed.
- Never compromise on type safety or error handling. These are non-negotiable requirements for Nango integrations.

---

#### Testing Guidelines
- **Dry Run Command**:
  - Use only `nango dryrun` for testing: `nango dryrun [action-name] -e prod [connection-id] --integration-id [provider-key] --input '{"param":"value"}'`.
  - Never use other terminal commands or testing methods.
- **Connection IDs**:
  - Run `pnpm connections-e` from the project root to list available connection IDs.
  - Prefer local connection IDs for testing.
- **Input Parameters**:
  - Ensure inputs are valid and realistic (e.g., use resource IDs that exist for the connection).
  - If possible, programmatically resolve inputs using existing actions (e.g., fetch a list of resources to get valid IDs).
- **Validation**:
  - After a successful dry run, run with `--validation` to confirm the output matches the schema.
  - If validation fails (e.g., “Invalid action output”), check for:
    - Missing fields in `nango.yaml` models (add them to match the API response).
    - Extra fields in the response (sanitize the response in the action to match the model).
  - If additional properties persist, update `nango.yaml` to include them.

#### Troubleshooting
- **“nango.yaml not found”**:
  - Confirm you’re in the correct provider directory.
  - Validate `nango.yaml` for errors (e.g., duplicate models, bad indentation) using:
    ```bash
    node -e "const yaml = require('js-yaml'); const fs = require('fs'); try { const doc = yaml.load(fs.readFileSync('nango.yaml', 'utf8')); console.log('YAML is valid'); } catch(e) { console.error('YAML Error:', e.message); }"
    ```
- **Schema Mismatches**:
  - If Nango reports additional properties, add them to the model in `nango.yaml`.
  - If validation errors persist, double-check the API response structure against the model.
- **Cyclical Dependencies**:
  - Resolve by creating specific sub-models for that particular case.
- **TypeScript Errors**:
  - Remove unused imports after model regeneration.
  - Ensure all input/output types are defined in `nango.yaml`.

### Additional Notes
- **No Prod Deployment**: Do not attempt to deploy to production or run `npm run nango:deploy`.
- **No Test Files**: Do not create test files or run tests outside `nango dryrun`.
- **Consult Resources**:
  - Before starting, review existing action files in the relevant package (`emcpe-nango-integrations` or `makeagent-nango-integrations`) for patterns.
  - Query Nango documentation or tools for guidance on writing action integrations.
- **Command Syntax**:
  - Use `nango generate`, not `pnpm nango generate`.
  - Run commands from the correct directory to avoid errors.

### Example Workflow
1. Navigate to the provider directory: `cd packages/emcpe-nango-integrations/dropbox`.
2. Update `nango.yaml` to define a new action and models.
3. Run `nango generate` to update `models.ts` and create `actions/[action-name].ts`.
4. Implement the action logic in `actions/[action-name].ts` with proper error handling.
5. Run `pnpm connections-e` from the root to get a `connection-id`.
6. Test with `nango dryrun [action-name] -e prod [connection-id] --integration-id dropbox --input '{"param":"value"}'`.
7. Validate with `nango dryrun [action-name] -e prod [connection-id] --integration-id dropbox --input '{"param":"value"}' --validation`.
8. Fix any errors by updating `nango.yaml` or the action implementation.
