import { MakeAgentFormat } from "./types";
import { modelToZodSchema } from "./zodHelpers";

export function generateSyncOutputsString(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const SYNC_OUTPUTS_STRING = `\n";
  output += "Supported Sync Outputs (Provider, Sync, Model, Description):\n";
  makeAgentFormat.syncOutputs.forEach((entry) => {
    const model = entry.model || "Unknown";
    const description = entry.description?.split("\n")[0] || "";
    output += `- ${entry.provider},${entry.sync},${model},${description}\n`;
  });
  output += "`;\n";
  return output;
}

export function generateActionInputsString(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_INPUTS_STRING = `\n";
  output += "Supported Action Inputs (Provider, Action, Model, Description):\n";
  makeAgentFormat.actionInputs.forEach((entry) => {
    const model = entry.model || "Unknown";
    const description = (entry.description || "").replace(/\n/g, " ").replace(/"/g, '\\"');
    output += `- ${entry.provider},${entry.action},${model},"${description}"\n`;
  });
  output += "`;\n";
  return output;
}

export function generateActionOutputsString(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_OUTPUTS_STRING = `\n";
  output += "Supported Action Outputs (Provider, Action, Model):\n";
  makeAgentFormat.actionOutputs.forEach((entry) => {
    const model = entry.model || "Unknown";
    output += `- ${entry.provider},${entry.action},${model}\n`;
  });
  output += "`;\n";
  return output;
}

export function generateSyncOutputs(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const SYNC_OUTPUTS = [\n";
  makeAgentFormat.syncOutputs.forEach((entry) => {
    const model = entry.model || null;
    const description = entry.description?.split("\n")[0]?.replace(/"/g, '\\"') || "";
    output += `  { provider: "${entry.provider}", sync: "${entry.sync}", model: ${
      JSON.stringify(
        model,
      )
    }, description: "${description}" },\n`;
  });
  output += "] as const;\n";
  return output;
}

export function generateActionInputs(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_INPUTS = [\n";
  makeAgentFormat.actionInputs.forEach((entry) => {
    const model = entry.model || null;
    const description = (entry.description || "").replace(/\n/g, " ").replace(/"/g, '\\"');
    output += `  { provider: "${entry.provider}", action: "${entry.action}", model: ${
      JSON.stringify(
        model,
      )
    }, description: "${description}" },\n`;
  });
  output += "] as const;\n";
  return output;
}

export function generateActionOutputs(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_OUTPUTS = [\n";
  makeAgentFormat.actionOutputs.forEach((entry) => {
    const model = entry.model || null;
    const description = (entry.description || "").replace(/\n/g, " ").replace(/"/g, '\\"');
    output += `  { provider: "${entry.provider}", action: "${entry.action}", model: ${
      JSON.stringify(
        model,
      )
    }, description: "${description}" },\n`;
  });
  output += "] as const;\n";
  return output;
}

export function generateActionInputModelsZod(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_INPUT_MODELS_ZOD: Record<string, z.ZodObject<any>> = {\n";
  Object.entries(makeAgentFormat.actionInputModelsDictionary).forEach(([modelName, model]) => {
    output += modelToZodSchema(model, modelName, "ACTION_INPUT_MODELS_ZOD");
  });
  output += "};\n";
  return output;
}

export function generateActionOutputModelsZod(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_OUTPUT_MODELS_ZOD: Record<string, z.ZodObject<any>> = {\n";
  Object.entries(makeAgentFormat.actionOutputModelsDictionary).forEach(([modelName, model]) => {
    output += modelToZodSchema(model, modelName, "ACTION_OUTPUT_MODELS_ZOD");
  });
  output += "};\n";
  return output;
}

export function generateSyncOutputModelsZod(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const SYNC_OUTPUT_MODELS_ZOD: Record<string, z.ZodObject<any>> = {\n";
  Object.entries(makeAgentFormat.syncOutputModelsDictionary).forEach(([modelName, model]) => {
    output += modelToZodSchema(model, modelName, "SYNC_OUTPUT_MODELS_ZOD");
  });
  output += "};\n";
  return output;
}

export function generateActionInputModelsJsonSchema(makeAgentFormat: MakeAgentFormat): string {
  const jsonSchema = Object.entries(makeAgentFormat.actionInputModelsDictionary).reduce(
    (acc, [modelName, model]) => {
      const properties: Record<string, any> = {};
      const required: string[] = [];

      Object.entries(model).forEach(([fieldName, field]) => {
        let type: any = typeof field.type === "string" ? field.type : field.type.$ref;
        if (field.union && field.union.length > 1) {
          type = field.union;
        } else if (typeof field.type === "object" && "$ref" in field.type) {
          type = { $ref: field.type.$ref };
        } else if (field.type === "object") {
          type = "object";
        }

        const property: any = {
          type: field.array ? "array" : type,
        };
        if (field.array && typeof type !== "object" && !Array.isArray(type)) {
          property.items = { type };
        }
        if (!field.optional) {
          required.push(fieldName);
        }
        if (field.description) {
          property.description = field.description;
        }
        if (Array.isArray(type)) {
          property.type = type;
        }

        properties[fieldName] = property;
      });

      acc[modelName] = {
        type: "object",
        properties,
        required,
      };
      return acc;
    },
    {} as Record<string, any>,
  );

  return `export const ACTION_INPUT_MODELS_JSON_SCHEMA = ${JSON.stringify(jsonSchema, null, 2)};\n`;
}

export function generateActionOutputModelsJsonSchema(makeAgentFormat: MakeAgentFormat): string {
  const jsonSchema = Object.entries(makeAgentFormat.actionOutputModelsDictionary).reduce(
    (acc, [modelName, model]) => {
      const properties: Record<string, any> = {};
      const required: string[] = [];

      Object.entries(model).forEach(([fieldName, field]) => {
        let type: any = typeof field.type === "string" ? field.type : field.type.$ref;
        if (field.union && field.union.length > 1) {
          type = field.union;
        } else if (typeof field.type === "object" && "$ref" in field.type) {
          type = { $ref: field.type.$ref };
        } else if (field.type === "object") {
          type = "object";
        }

        const property: any = {
          type: field.array ? "array" : type,
        };
        if (field.array && typeof type !== "object" && !Array.isArray(type)) {
          property.items = { type };
        }
        if (!field.optional) {
          required.push(fieldName);
        }
        if (field.description) {
          property.description = field.description;
        }
        if (Array.isArray(type)) {
          property.type = type;
        }

        properties[fieldName] = property;
      });

      acc[modelName] = {
        type: "object",
        properties,
        required,
      };
      return acc;
    },
    {} as Record<string, any>,
  );

  return `export const ACTION_OUTPUT_MODELS_JSON_SCHEMA = ${
    JSON.stringify(jsonSchema, null, 2)
  };\n`;
}

export function generateSyncOutputModelsJsonSchema(makeAgentFormat: MakeAgentFormat): string {
  const jsonSchema = Object.entries(makeAgentFormat.syncOutputModelsDictionary).reduce(
    (acc, [modelName, model]) => {
      const properties: Record<string, any> = {};
      const required: string[] = [];

      Object.entries(model).forEach(([fieldName, field]) => {
        let type: any = typeof field.type === "string" ? field.type : field.type.$ref;
        if (field.union && field.union.length > 1) {
          type = field.union;
        } else if (typeof field.type === "object" && "$ref" in field.type) {
          type = { $ref: field.type.$ref };
        } else if (field.type === "object") {
          type = "object";
        }

        const property: any = {
          type: field.array ? "array" : type,
        };
        if (field.array && typeof type !== "object" && !Array.isArray(type)) {
          property.items = { type };
        }
        if (!field.optional) {
          required.push(fieldName);
        }
        if (field.description) {
          property.description = field.description;
        }
        if (Array.isArray(type)) {
          property.type = type;
        }

        properties[fieldName] = property;
      });

      acc[modelName] = {
        type: "object",
        properties,
        required,
      };
      return acc;
    },
    {} as Record<string, any>,
  );

  return `export const SYNC_OUTPUT_MODELS_JSON_SCHEMA = ${JSON.stringify(jsonSchema, null, 2)};\n`;
}

export function generateActionInputsKeyed(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_INPUTS_KEYED = {\n";
  makeAgentFormat.actionInputs.forEach((entry) => {
    if (entry.action && entry.model) {
      const key = `${entry.provider}:${entry.action}`;
      output +=
        `  "${key}": { model: "${entry.model}", zodSchema: ACTION_INPUT_MODELS_ZOD["${entry.model}"], jsonSchema: ACTION_INPUT_MODELS_JSON_SCHEMA["${entry.model}"] },\n`;
    }
  });
  output += "};\n";
  return output;
}

export function generateActionOutputsKeyed(makeAgentFormat: MakeAgentFormat): string {
  let output = "export const ACTION_OUTPUTS_KEYED = {\n";
  makeAgentFormat.actionInputs.forEach((entry) => {
    if (entry.action && entry.model) {
      const key = `${entry.provider}:${entry.action}`;
      output +=
        `  "${key}": { model: "${entry.model}", zodSchema: ACTION_OUTPUT_MODELS_ZOD["${entry.model}"], jsonSchema: ACTION_OUTPUT_MODELS_JSON_SCHEMA["${entry.model}"] },\n`;
    }
  });
  output += "};\n";
  return output;
}
