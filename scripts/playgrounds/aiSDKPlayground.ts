import { openai } from "@ai-sdk/openai";
import { generateObject, jsonSchema } from "ai";
import { z } from "zod";
import dotenv from "dotenv";
dotenv.config();

// Define the JSON schema with optional fields
const userJsonSchema = {
  type: 'object',
  properties: {
    name: {
      type: 'string',
      description: 'The user’s full name',
    },
    email: {
      type: 'string',
      description: 'The user’s email address',
    },
    phone: {
      type: ['string', 'null'], // Allow null to make it optional
      description: 'The user’s phone number (optional)',
    },
    age: {
      type: 'number', // Allow null to make it optional
      description: 'The user’s age (optional)',
    },
  },
  required: ['name', 'email'], // Only name and email are required
  additionalProperties: false, // Prevent extra fields
};

// Function to extract user info
async function extractUserInfo(text) {
  try {
    const { object } = await generateObject({
      model: openai.responses('gpt-4.1'), // Use a model that supports structured outputs
      schema: jsonSchema(userJsonSchema),
      prompt: `Extract user information from the following text:\n\n${text}`,
      system: 'You are a user information extraction assistant.',
    });
    return object;
  } catch (error) {
    console.error('Error:', error.message);
    if (error.rawOutput) console.log('Raw output:', error.rawOutput);
    throw error;
  }
}

// Test with text missing optional fields
const text = 'John Doe, email: <EMAIL>';
extractUserInfo(text)
  .then(result => console.log(result))
  .catch(console.error);

// async function actionsAgent(messages: any[]) {
//   try {
//     console.log("Starting actionsAgent with messages:", messages);

//     const result = streamText({
//       model: openai.responses("gpt-4.1"),
//       system: `

// `,
//       messages,
//       providerOptions: {
//         openai: {
//           // previousResponseId: "resp_68212dfecca48191881018db2c6fbbc80b905778e4e51e85",
//           strictSchemas: false,
//         },
//       },
//       tools: {
//         actionCall: tool({
//           description: "Call an action",
//           parameters: jsonSchema({
//             "type": "object",
//             "properties": {
//               "actionName": {
//                 "type": "string",
//                 "description": "The name of the action to call",
//               },
//               "actionParameters": {
//                 "type": "object",
//                 "description": "Pass a few random parameters",
//                 "additionalProperties": true,
//               },
//             },
//             "additionalProperties": false,
//             "required": [
//               "actionName",
//               "actionParameters"
//             ],
//           }),
//         }),
//       },
//     });

//     const stream = result.toDataStream();
//     // Create a reader for the stream
//     const reader = stream.getReader();

//     // Function to read and log stream chunks
//     async function readStream() {
//       while (true) {
//         const { done, value } = await reader.read().catch((error) => {
//           console.error("Error reading stream:", error);
//           throw error;
//         });
//         if (done) {
//           console.log("Stream completed");
//           break;
//         }
//         // Convert the chunk (Uint8Array) to string and log it
//         const chunk = new TextDecoder().decode(value);
//         console.log("Response chunk:", chunk);
//       }
//     }

//     // Start reading the stream
//     await readStream().catch((error) => {
//       console.error("Error in readStream:", error);
//       throw error;
//     });

//     console.log((await result.providerMetadata)?.openai?.responseId)

//     return undefined;
//   } catch (error) {
//     console.error("Fatal error in actionsAgent:", error);
//     throw error;
//   }
// }

// export { actionsAgent };

// // Example usage
// (async () => {
//   try {
//     console.log("Executing actionsAgent");
//     await actionsAgent([
//       vercel.userMessage(
//         "What messages exist in this conversation so far?",
//       ),
//       // vercel.toolCallMessage({
//       //   toolCallId: "call_vdY7nqZA1mbmS0ufPnFZusn2",
//       //   toolName: "actionCall",
//       //   args: {
//       //     actionName: "test",
//       //     args: {},
//       //   },
//       // }),
//       // vercel.toolResultMessage({
//       //   toolCallId: "call_vdY7nqZA1mbmS0ufPnFZusn2",
//       //   toolName: "actionCall",
//       //   result: "this was a success",
//       // }),
//     ]);
//     console.log("actionsAgent execution completed");
//   } catch (error) {
//     console.error("Error in main execution:", error);
//   }
// })();
