integrations:
    dropbox:
        actions:
            list-files:
                endpoint: GET /list-files
                description: List files and folders in a Dropbox directory
                input: DropboxListFilesInput
                output: DropboxFileList
                scopes:
                    - files.metadata.read
            get-file:
                endpoint: GET /get-file
                description: Get file metadata and a download URL (not the actual file content)
                input: DropboxGetFileInput
                output: DropboxFile
                scopes:
                    - files.metadata.read
                    - files.content.read
            upload-file:
                endpoint: POST /upload-file
                description: Upload string content as a file to Dropbox
                input: DropboxUploadFileInput
                output: DropboxFile
                scopes:
                - files.metadata.write
                - files.content.write
            create-folder:
                endpoint: POST /create-folder
                description: Create a new folder in Dropbox and return folder metadata
                input: DropboxCreateFolderInput
                output: DropboxFolder
                scopes:
                    - files.metadata.write
            delete-file:
                endpoint: POST /delete-file
                description: Delete a file or folder in Dropbox and return metadata of the deleted item
                input: DropboxDeleteInput
                output: DropboxDeleteResult
                scopes:
                    - files.content.write
            move-file:
                endpoint: POST /move-file
                description: Move a file or folder to a different location in Dropbox and return metadata of the moved item
                input: DropboxMoveInput
                output: DropboxEntry
                scopes:
                    - files.content.write
            copy-file:
                endpoint: POST /copy-file
                description: Copy a file or folder to a different location in Dropbox and return metadata of the copied item
                input: DropboxCopyInput
                output: DropboxEntry
                scopes:
                    - files.content.write
            search-files:
                endpoint: POST /search-files
                description: Search for files and folders in Dropbox by filename or content
                input: DropboxSearchInput
                output: DropboxSearchResult
                scopes:
                    - files.metadata.read
            # Removed get-account-info and get-space-usage actions
            # These actions cannot be implemented with Nango due to Dropbox API requirements
            # for POST requests with null bodies

models:
    DropboxListFilesInput:
        path: string                        # The path to list (defaults to root if empty)
        recursive?: boolean      # Whether to list files recursively
        limit?: number                 # Maximum number of files to return
        include_deleted?: boolean # Whether to include deleted files

    DropboxFileList:
        entries: DropboxEntry[] # List of files and folders
        cursor?: string            # Cursor for pagination
        has_more: boolean         # Whether there are more entries

    DropboxEntry:
        .tag: string                      # 'file' or 'folder'
        id: string                        # Unique identifier
        name: string                      # File or folder name
        path_display: string              # Full path to the file or folder
        path_lower: string                # Lowercase full path
        client_modified?: string          # Last client modified time (for files)
        server_modified?: string          # Last server modified time (for files)
        rev?: string                      # Revision (for files)
        size?: number                     # Size in bytes (for files)
        is_downloadable?: boolean         # Is downloadable (for files)
        content_hash?: string             # Content hash (for files)

    DropboxGetFileInput:
        path: string                     # The path to the file

    DropboxFile:
        id: string                         # Unique identifier
        name: string                     # File name
        path_display: string     # Full path to the file
        path_lower: string         # Lowercase full path
        size: number                     # Size in bytes
        content_hash?: string   # Content hash
        server_modified: string # Last modified time
        content?: string             # Base64 encoded file content
        content_type?: string     # MIME type of the file
        download_url?: string     # Direct download URL for the file

    DropboxUploadFileInput:
        path: string                     # The path where to upload the file
        content: string            # Base64 encoded file content
        mode?: string               # Upload mode: 'add', 'overwrite', or 'update' (default: 'add')
        autorename?: boolean     # Whether to auto-rename the file if the path already exists
        mute?: boolean                 # Whether to mute notifications

    DropboxCreateFolderInput:
        path: string                     # The path where to create the folder
        autorename?: boolean     # Whether to auto-rename the folder if the path already exists

    DropboxFolder:
        id: string                         # Unique identifier
        name: string                     # Folder name
        path_display: string     # Full path to the folder
        path_lower: string         # Lowercase full path

    DropboxDeleteInput:
        path: string                     # The path of the file or folder to delete

    DropboxDeleteResult:
        metadata: DropboxEntry     # Metadata of the deleted file or folder

    DropboxMoveInput:
        from_path: string           # The path to the file or folder to be moved
        to_path: string               # The path where to move the file or folder
        allow_shared_folder?: boolean  # Whether to allow moving between shared folders
        autorename?: boolean     # Whether to auto-rename the file if the target path already exists
        allow_ownership_transfer?: boolean  # Whether to allow ownership transfer

    DropboxCopyInput:
        from_path: string           # The path to the file or folder to be copied
        to_path: string               # The path where to copy the file or folder
        allow_shared_folder?: boolean  # Whether to allow copying between shared folders
        autorename?: boolean     # Whether to auto-rename the file if the target path already exists

    DropboxSearchInput:
        query: string                   # The search query
        path?: string                   # The path to search in (defaults to root)
        max_results?: number     # Maximum number of results to return
        mode?: string                 # Search mode: 'filename', 'content', or 'deleted_filename'

    DropboxSearchResult:
        matches: DropboxSearchMatch[]  # List of search matches
        more: boolean                # Whether there are more results
        start: number                 # Starting index for pagination

    DropboxSearchMatch:
        metadata: DropboxEntry   # File or folder metadata
        match_type: string         # Type of match (e.g., 'filename', 'content')
