import type {
  DropboxSearchInput,
  DropboxSearchResult,
  NangoAction,
} from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: DropboxSearchInput
): Promise<DropboxSearchResult | NangoError> {
  try {
    const { query, path = '', max_results = 100, mode = 'filename' } = input;
    const start = 0;

    if (!query) {
      return { error: { status: 400, message: 'Input validation failed: Query is required' } };
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/search_v2',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        query,
        options: {
          path: path || '',
          max_results,
          mode: {
            '.tag': mode,
          },
        },
        match_field_options: {
          include_highlights: false,
        },
        start,
      },
      retries: 3,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error searching files in Dropbox:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error_summary ||
      error?.message ||
      'An unknown error occurred while searching files in Dropbox.';
    return { error: { status, message } };
  }
}
