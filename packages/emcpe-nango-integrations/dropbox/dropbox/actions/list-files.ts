import type {
  DropboxFileList,
  DropboxListFilesInput,
  NangoAction,
} from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: DropboxListFilesInput
): Promise<DropboxFileList | NangoError> {
  try {
    const { path = '', recursive = false, limit = 100, include_deleted = false } = input;

    const requestBody = {
      path: path || '',
      recursive,
      limit,
      include_deleted,
      include_media_info: false,
      include_has_explicit_shared_members: false,
    };

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/list_folder',
      headers: {
        'Content-Type': 'application/json',
      },
      data: requestBody,
      retries: 3,
    });

    return response.data;
  } catch (error: any) {
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error_summary ||
      error?.message ||
      'An unknown error occurred while listing Dropbox files.';
    return { error: { status, message } };
  }
}
