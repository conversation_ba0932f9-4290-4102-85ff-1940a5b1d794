integrations:
    x-social:
        actions:
            send-post:
                endpoint: POST /send-post
                description: Sends a new post to <PERSON>.
                input: XSocialPostInput
                output: XSocialPostOutput
                scopes:
                  - tweet.read
                  - tweet.write
                  - users.read
            get-user-profile:
                endpoint: GET /get-user-profile
                description: Gets the authenticated user's profile information from X.
                output: XSocialUserProfile
                scopes:
                  - users.read

models:
    XSocialPostInput:
        text: string            # The text content of the post
        reply_to?: string       # Optional ID of a post to reply to
        quote?: string          # Optional ID of a post to quote
    XSocialPostOutput:
        id: string              # The ID of the created post
        text: string            # The text of the post
        created_at: string      # When the post was created
    XSocialUserProfile:
        id: string              # User ID
        name: string            # User name
        username: string        # User username
        profile_image_url: string  # URL of the user's profile image
        description: string     # User description
        location: string        # User location
        url: string             # User URL
        protected: boolean      # Whether the user's tweets are protected
        verified: boolean       # Whether the user is verified
        followers_count: number # Number of followers
        following_count: number # Number of users being followed
        tweet_count: number     # Number of tweets
        listed_count: number    # Number of lists the user is a member of
