integrations:
    github:
        actions:
            # Repository CRUD actions
            list-repositories:
                endpoint: GET /list-repositories
                description: Lists repositories for the authenticated user.
                output: GithubRepositoryList
                scopes:
                  - repo
            get-repository:
                endpoint: GET /get-repository
                description: Gets a specific repository by owner and name.
                input: GithubRepositoryInput
                output: GithubRepository
                scopes:
                  - repo
            create-repository:
                endpoint: POST /create-repository
                description: Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url.
                input: GithubCreateRepositoryInput
                output: GithubRepository
                scopes:
                  - repo
            update-repository:
                endpoint: PATCH /update-repository
                description: Updates an existing repository.
                input: GithubUpdateRepositoryInput
                output: GithubRepository
                scopes:
                  - repo
            delete-repository:
                endpoint: DELETE /delete-repository
                description: Deletes a repository.
                input: GithubRepositoryInput
                output: GithubDeleteRepositoryOutput
                scopes:
                  - delete_repo
            create-organization-repository:
                endpoint: POST /create-organization-repository # Static endpoint path
                description: Creates a new repository within a specified organization.
                input: GithubCreateOrganizationRepositoryInput
                output: GithubRepository
                scopes:
                  - repo
                  - admin:org # Required for creating org repos
            # Issue CRUD actions
            list-issues:
                endpoint: GET /list-issues
                description: Lists issues for a repository.
                input: GithubIssuesInput
                output: GithubIssueList
                scopes:
                  - repo
            get-issue:
                endpoint: GET /get-issue
                description: Gets a specific issue by number.
                input: GithubIssueInput
                output: GithubIssue
                scopes:
                  - repo
            create-issue:
                endpoint: POST /create-issue
                description: Creates a new issue in a repository.
                input: GithubCreateIssueInput
                output: GithubIssue
                scopes:
                  - repo
            update-issue:
                endpoint: PATCH /update-issue
                description: Updates an existing issue.
                input: GithubUpdateIssueInput
                output: GithubIssue
                scopes:
                  - repo
            # Pull Request actions
            get-pull-request:
                endpoint: GET /get-pull-request
                description: Get details of a specific pull request in a GitHub repository.
                input: GithubPullRequestInput
                output: GithubPullRequest
                scopes:
                  - pull
            update-pull-request:
                endpoint: PATCH /update-pull-request
                description: Update an existing pull request in a GitHub repository.
                input: GithubUpdatePullRequestInput
                output: GithubPullRequest
                scopes:
                  - pull
            list-pull-requests:
                endpoint: GET /list-pull-requests
                description: List pull requests in a GitHub repository.
                input: GithubListPullRequestsInput
                output: GithubPullRequestList
                scopes:
                  - pull
            merge-pull-request:
                endpoint: PUT /merge-pull-request
                description: Merge a pull request in a GitHub repository.
                input: GithubMergePullRequestInput
                output: GithubMergeResult
                scopes:
                  - pull
            get-pull-request-files:
                endpoint: GET /get-pull-request-files
                description: Get the files changed in a specific pull request.
                input: GithubPullRequestInput
                output: GithubPullRequestFileList
                scopes:
                  - pull
            get-pull-request-status:
                endpoint: GET /get-pull-request-status
                description: Get the combined status of all status checks for a pull request.
                input: GithubPullRequestInput
                output: GithubCombinedStatus
                scopes:
                  - status
            update-pull-request-branch:
                endpoint: PUT /update-pull-request-branch
                description: Update the branch of a pull request with the latest changes from the base branch.
                input: GithubUpdatePullRequestBranchInput
                output: GithubBranchUpdateResult
                scopes:
                  - pull
            get-pull-request-comments:
                endpoint: GET /get-pull-request-comments
                description: Get the review comments on a pull request.
                input: GithubPullRequestInput
                output:
                    - GithubPullRequestCommentList
                scopes:
                  - pull
            add-pull-request-review-comment:
                endpoint: POST /add-pull-request-review-comment
                description: Add a review comment to a pull request.
                input: GithubAddPullRequestReviewCommentInput
                output: GithubPullRequestComment
                scopes:
                  - pull
            create-pull-request-review:
                endpoint: POST /create-pull-request-review
                description: Submit a review on a pull request.
                input: GithubCreatePullRequestReviewInput
                output: GithubPullRequestReview
                scopes:
                  - pull
            create-pull-request:
                endpoint: POST /create-pull-request
                description: Create a new pull request in a GitHub repository.
                input: GithubCreatePullRequestInput
                output: GithubPullRequest
                scopes:
                  - pull

models:
    GithubRepositoryInput:
        owner: string       # Repository owner (username or organization)
        repo: string        # Repository name

    GithubRepository:
        id: number          # Repository ID
        node_id: string
        name: string        # Repository name
        full_name: string   # Full repository name (owner/repo)
        private: boolean    # Whether the repository is private
        owner: GithubIssueCreator # Owner of the repository
        html_url: string    # URL to the repository on GitHub
        description: string | null # Repository description
        fork: boolean       # Whether the repository is a fork
        url: string         # API URL for the repository
        forks_url: string
        keys_url: string
        collaborators_url: string
        teams_url: string
        hooks_url: string
        issue_events_url: string
        events_url: string
        assignees_url: string
        branches_url: string
        tags_url: string
        blobs_url: string
        git_tags_url: string
        git_refs_url: string
        trees_url: string
        statuses_url: string
        languages_url: string
        stargazers_url: string
        contributors_url: string
        subscribers_url: string
        subscription_url: string
        commits_url: string
        git_commits_url: string
        comments_url: string
        issue_comment_url: string
        contents_url: string
        compare_url: string
        merges_url: string
        archive_url: string
        downloads_url: string
        issues_url: string
        pulls_url: string
        milestones_url: string
        notifications_url: string
        labels_url: string
        releases_url: string
        deployments_url: string
        created_at: string  # Creation date
        updated_at: string  # Last update date
        pushed_at: string   # Last push date
        git_url: string     # Git URL
        ssh_url: string     # SSH URL
        clone_url: string   # HTTPS clone URL
        svn_url: string
        homepage: string | null
        size: number
        stargazers_count: number
        watchers_count: number
        language: string | null # Primary language
        has_issues: boolean
        has_projects: boolean
        has_downloads: boolean
        has_wiki: boolean
        has_pages: boolean
        has_discussions: boolean
        forks_count: number
        mirror_url: string | null
        archived: boolean
        disabled: boolean
        open_issues_count: number # Number of open issues
        license: string | null # License information
        allow_forking: boolean
        is_template: boolean
        web_commit_signoff_required: boolean
        topics: string[]    # Repository topics
        visibility: string  # Repository visibility (public, private, internal)
        forks: number
        open_issues: number
        watchers: number
        default_branch: string # Default branch name

    GithubRepositoryList:
        note: string | null # to allow us to point out about non-user repos
        repositories: GithubRepository[] # List of repositories

    GithubCreateRepositoryInput:
        name: string        # Repository name
        description?: string # Repository description
        private?: boolean   # Whether the repository is private
        has_issues?: boolean # Whether to enable issues
        has_projects?: boolean # Whether to enable projects
        has_wiki?: boolean  # Whether to enable wiki
        auto_init?: boolean # Whether to initialize with a README
        gitignore_template?: string # Gitignore template to use
        license_template?: string # License template to use

    GithubUpdateRepositoryInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        name?: string       # New repository name
        description?: string # New description
        private?: boolean   # New private status
        has_issues?: boolean # New has_issues status
        has_projects?: boolean # New has_projects status
        has_wiki?: boolean  # New has_wiki status
        default_branch?: string # New default branch

    GithubDeleteRepositoryOutput:
        success: boolean    # Whether the deletion was successful
        message: string     # Status message

    GithubCreateOrganizationRepositoryInput:
        org: string         # Organization name
        name: string        # Repository name
        description?: string # Repository description
        homepage?: string   # Repository homepage URL
        private?: boolean   # Whether the repository is private (default: false)
        has_issues?: boolean # Whether to enable issues (default: true)
        has_projects?: boolean # Whether to enable projects (default: true)
        has_wiki?: boolean  # Whether to enable wiki (default: true)
        # Note: auto_init, gitignore_template, license_template are also available but omitted for simplicity

    GithubIssuesInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        state?: string      # Issue state (open, closed, all)
        sort?: string       # Sort field (created, updated, comments)
        direction?: string  # Sort direction (asc, desc)
        per_page?: number   # Results per page
        page?: number       # Page number

    GithubIssueInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        issue_number: number # Issue number

    GithubIssue:
        id: number          # Issue ID
        node_id: string     # Node ID
        url: string         # API URL for the issue
        repository_url: string # API URL for the repository
        labels_url: string  # URL for issue labels
        comments_url: string # URL for issue comments
        events_url: string  # URL for issue events
        html_url: string    # URL to the issue on GitHub
        number: number      # Issue number
        title: string       # Issue title
        state: string       # Issue state (open, closed)
        locked: boolean     # Whether the issue is locked
        body: string | null # Issue body
        user: GithubIssueCreatorLite | GithubIssueCreator # User who created the issue
        labels: GithubIssueLabel[] # Issue labels
        assignee: object | null # Single assignee
        assignees: object[] # Issue assignees
        milestone: string | null # Milestone
        comments: number    # Number of comments
        created_at: string  # Creation date
        updated_at: string  # Last update date
        closed_at: string | null # Closing date
        author_association: string # Author association (e.g., NONE)
        active_lock_reason: string | null # Reason for the issue being locked
        sub_issues_summary: GithubSubIssuesSummary # Summary of sub-issues
        closed_by: GithubIssueCreator | null # User who closed the issue
        reactions: GithubReactions # Reaction counts for the issue
        timeline_url: string # URL for issue timeline
        performed_via_github_app: object | null # GitHub App used
        state_reason: string | null # Reason for the issue's state

    GithubSubIssuesSummary:
      total: number       # Total number of sub-issues
      completed: number   # Number of completed sub-issues
      percent_completed: number # Percentage of sub-issues completed

    GithubIssueCreatorLite:
      login: string       # Username
      id: number          # User ID
      avatar_url: string  # Avatar URL
      html_url: string    # URL to the user on GitHub

    # User who created the issue
    GithubIssueCreator:
      login: string       # Username
      id: number          # User ID
      node_id: string
      avatar_url: string  # Avatar URL
      gravatar_id: string # Gravatar ID
      url: string         # API URL for the user
      html_url: string    # URL to the user on GitHub
      followers_url: string
      following_url: string
      gists_url: string
      starred_url: string
      subscriptions_url: string
      organizations_url: string
      repos_url: string
      events_url: string
      received_events_url: string
      type: string
      user_view_type: string # Visibility type of the user (e.g., public)
      site_admin: boolean

    # Issue label
    GithubIssueLabel:
        id: number          # Label ID
        name: string        # Label name
        color: string       # Label color
        description: string # Label description

    # Issue assignee
    GithubIssueAssignee:
        id: number          # User ID
        login: string       # Username
        avatar_url: string  # Avatar URL
        html_url: string    # URL to the user on GitHub

    GithubIssueList:
        issues: GithubIssue[] # List of issues

    GithubCreateIssueInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        title: string       # Issue title
        body?: string       # Issue body
        assignees?: string[] # Usernames of assignees
        labels?: string[]   # Label names

    GithubUpdateIssueInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        issue_number: number
        title?: string
        body?: string
        state?: string
        assignees?: string[]
        labels?: string[]

    # Pull Request Models
    GithubPullRequestInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        pullNumber: number  # Pull request number

    GithubPullRequest:
        url: string
        id: number          # Pull request ID
        node_id: string
        html_url: string    # URL to the pull request on GitHub
        diff_url: string    # URL to the diff
        patch_url: string   # URL to the patch
        issue_url: string   # URL to the associated issue
        number: number      # Pull request number
        state: string       # Pull request state (open, closed)
        locked: boolean
        title: string       # Pull request title
        user: GithubIssueCreator # User who created the pull request
        body: string | null # Pull request body
        created_at: string  # Creation date
        updated_at: string  # Last update date
        closed_at: string | null # Closing date
        merged_at: string | null # Merging date
        merge_commit_sha: string | null # SHA of the merge commit
        assignee: GithubIssueAssignee | null # Assignee
        assignees: GithubIssueAssignee[] # Assignees
        requested_reviewers: GithubIssueAssignee[] # Requested reviewers
        requested_teams: GithubTeamRef[] # Requested teams
        labels: GithubIssueLabel[] # Pull request labels
        milestone: string | null # Milestone
        draft: boolean
        commits_url: string
        review_comments_url: string
        review_comment_url: string
        comments_url: string
        statuses_url: string
        head: object
        base: object # also GithubPullRequestBranch
        _links: object # Links related to the pull request
        author_association: string # Author association
        auto_merge: object | null # Auto merge details
        active_lock_reason: string | null # Reason for the pull request being locked
        merged: boolean     # Whether the pull request is merged
        mergeable: boolean | null # Whether the pull request is mergeable
        rebaseable: boolean | null # Whether the pull request is rebaseable
        mergeable_state: string # State of mergeability
        merged_by: GithubIssueCreator | null # User who merged the pull request
        comments: number    # Number of comments
        review_comments: number # Number of review comments
        maintainer_can_modify: boolean
        commits: number    # Number of commits
        additions: number   # Number of additions
        deletions: number   # Number of deletions
        changed_files: number # Number of changed files

    GithubPullRequestBranch:
        label: string       # Label (e.g., owner:branch)
        ref: string         # Branch name
        sha: string         # Commit SHA
        user: GithubIssueCreator # User/organization

    GithubTeam:
        id: number          # Team ID
        name: string        # Team name
        slug: string        # Team slug
        description: string # Team description
        privacy: string     # Team privacy
        url: string         # API URL for the team
        html_url: string    # HTML URL for the team
        members_url: string # URL for team members
        repositories_url: string # URL for team repositories
        permission: string  # Team permission

    GithubUpdatePullRequestInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        pullNumber: number  # Pull request number to update
        title?: string      # New title
        body?: string       # New description
        state?: string      # New state (open, closed)
        base?: string       # New base branch name
        maintainer_can_modify?: boolean # Allow maintainer edits

    GithubListPullRequestsInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        state?: string      # Filter by state (open, closed, all)
        head?: string       # Filter by head user/org and branch
        base?: string       # Filter by base branch
        sort?: string       # Sort by (created, updated, popularity, long-running)
        direction?: string  # Sort direction (asc, desc)
        per_page?: number   # Results per page
        page?: number       # Page number

    GithubPullRequestList:
        pull_requests: GithubPullRequest[] # List of pull requests

    GithubMergePullRequestInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        pullNumber: number  # Pull request number
        commit_title?: string # Title for merge commit
        commit_message?: string # Extra detail for merge commit
        merge_method?: string # Merge method (merge, squash, rebase)

    GithubMergeResult:
        sha: string         # SHA of the merge commit
        merged: boolean     # Whether the merge was successful
        message: string     # Merge status message

    GithubPullRequestFile:
        sha: string         # File SHA
        filename: string    # File name
        status: string      # File status (added, modified, removed, etc.)
        additions: number   # Number of additions
        deletions: number   # Number of deletions
        changes: number     # Total changes
        blob_url: string    # URL to the blob
        raw_url: string     # URL to the raw file
        contents_url: string # URL to the contents API
        patch: string       # Diff patch

    GithubPullRequestFileList:
        files: GithubPullRequestFile[] # List of changed files

    GithubCombinedStatus:
        state: string
        sha: string
        total_count: number
        statuses: GithubStatus[]
        repository: GithubRepositoryForGithubCombinedStatus
        commit_url: string

    GithubRepositoryForGithubCombinedStatus:
        id: number
        node_id: string
        name: string
        full_name: string
        private: boolean
        owner: GithubIssueCreator
        html_url: string
        description: string
        fork: boolean
        url: string
        forks_url: string
        keys_url: string
        collaborators_url: string
        teams_url: string
        hooks_url: string
        issue_events_url: string
        events_url: string
        assignees_url: string
        branches_url: string
        tags_url: string
        blobs_url: string
        git_tags_url: string
        git_refs_url: string
        trees_url: string
        statuses_url: string
        languages_url: string
        stargazers_url: string
        contributors_url: string
        subscribers_url: string
        subscription_url: string
        commits_url: string
        git_commits_url: string
        comments_url: string
        issue_comment_url: string
        contents_url: string
        compare_url: string
        merges_url: string
        archive_url: string
        downloads_url: string
        issues_url: string
        pulls_url: string
        milestones_url: string
        notifications_url: string
        labels_url: string
        releases_url: string
        deployments_url: string

    GithubStatus:
        url: string         # API URL for the status
        id: number          # Status ID
        node_id: string     # Node ID
        state: string       # Status state (success, failure, pending, error)
        context: string     # Status context
        description: string # Status description
        target_url: string  # URL for more details
        created_at: string  # Creation date
        updated_at: string  # Last update date

    GithubUpdatePullRequestBranchInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        pullNumber: number  # Pull request number
        expectedHeadSha?: string # The expected SHA of the pull request's HEAD ref

    GithubBranchUpdateResult:
        message: string     # Update status message
        url: string         # URL to the pull request

    GithubPullRequestComment:
        id: number          # Comment ID
        node_id: string     # Node ID
        url: string         # API URL for the comment
        pull_request_review_id: number # ID of the pull request review
        diff_hunk: string   # Diff hunk where the comment is placed
        path: string        # File path
        position: number    # Position in diff
        original_position: number # Original position in diff
        commit_id: string   # Commit SHA
        original_commit_id: string # Original commit SHA
        user: GithubIssueCreator # User who created the comment
        body: string        # Comment body
        created_at: string  # Creation date
        updated_at: string  # Last update date
        html_url: string    # HTML URL for the comment
        pull_request_url: string # API URL for the pull request
        author_association: string # Author association
        _links: object       # Links related to the comment
        reactions: GithubReactions # Reaction counts for the comment
        start_line: number | null # Starting line of comment range
        original_start_line: number | null # Original starting line
        start_side: string | null # Side of starting line (e.g., LEFT, RIGHT)
        line: number        # Ending line of comment
        original_line: number # Original ending line
        side: string        # Side of ending line (e.g., LEFT, RIGHT)
        in_reply_to_id?: number # ID of the comment this is replying to
        subject_type: string # Type of comment subject (e.g., line, file)

    GithubReactions:
        url: string         # API URL for reactions
        total_count: number # Total number of reactions
        "+1": number        # Number of +1 reactions
        '-1': number        # Number of -1 reactions
        laugh: number       # Number of laugh reactions
        hooray: number      # Number of hooray reactions
        confused: number    # Number of confused reactions
        heart: number       # Number of heart reactions
        rocket: number      # Number of rocket reactions
        eyes: number        # Number of eyes reactions

    GithubPullRequestCommentList:
        comments: GithubPullRequestComment[] # List of comments

    GithubAddPullRequestReviewCommentInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        pull_number: number # Pull request number
        body: string        # The text of the review comment
        commit_id?: string  # The SHA of the commit to comment on. Required unless in_reply_to is specified.
        path?: string       # The relative path to the file that necessitates a comment. Required unless in_reply_to is specified.
        subject_type?: string # The level at which the comment is targeted (line, file)
        line?: number       # The line of the blob in the pull request diff that the comment applies to. For multi-line comments, the last line of the range
        side?: string       # The side of the diff to comment on (LEFT, RIGHT)
        start_line?: number # The first line of the range that the comment applies to
        start_side?: string # The side of the diff on which the start line resides for multi-line comments. (LEFT or RIGHT)
        in_reply_to?: number # The ID of the review comment to reply to. When specified, only body is required and all other parameters are ignored
        diff_hunk?: string

    GithubPullRequestReview:
        id: number          # Review ID
        node_id: string     # Node ID
        user: GithubIssueCreator # User who created the review
        body: string        # Review body
        state: string       # Review state (APPROVED, CHANGES_REQUESTED, COMMENTED, DISMISSED, PENDING)
        html_url: string    # HTML URL for the review
        pull_request_url: string # API URL for the pull request
        submitted_at: string # Submission date
        commit_id: string   # Commit SHA
        author_association: string # Author association
        _links: object      # Links related to the review

    GithubCreatePullRequestReviewInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        pullNumber: number  # Pull request number
        body?: string       # Review comment text
        event: string       # Review action to perform (APPROVE, REQUEST_CHANGES, COMMENT)
        commitId?: string   # SHA of commit to review
        comments?: GithubDraftReviewComment[] # Line-specific comments array

    GithubDraftReviewComment:
        path: string        # path to the file
        position?: number   # position of the comment in the diff
        line?: number       # line number in the file to comment on. For multi-line comments, the end of the line range
        side?: string       # The side of the diff on which the line resides. For multi-line comments, this is the side for the end of the line range. (LEFT or RIGHT)
        start_line?: number # The first line of the range that the comment refers to. Required for multi-line comments.
        start_side?: string # The side of the diff on which the start line resides for multi-line comments. (LEFT or RIGHT)
        body: string        # comment body

    GithubCreatePullRequestInput:
        owner: string       # Repository owner
        repo: string        # Repository name
        title: string       # PR title
        body?: string       # PR description
        head: string        # Branch containing changes
        base: string        # Branch to merge into
        draft?: boolean     # Create as draft PR
        maintainer_can_modify?: boolean # Allow maintainer edits

    GithubTeamRef:
        id: number          # Team ID
        name: string        # Team name
