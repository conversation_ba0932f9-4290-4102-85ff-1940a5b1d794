import type { GoogleCalendar<PERSON><PERSON><PERSON><PERSON>, GoogleCalendarEventsInput, NangoAction } from "../../models";

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists events from a specified calendar with optional filtering.
 *
 * @param nango - The Nango SDK instance
 * @param input - Parameters for filtering events, including calendarId (required)
 * @returns A list of events and a nextPageToken if more results are available
 */
export default async function runAction(
  nango: NangoAction,
  input: GoogleCalendarEventsInput,
): Promise<GoogleCalendarEventList | NangoError> {
  try {
    if (!input.calendarId) {
      return { error: { status: 400, message: "Input validation failed: calendarId is required" } };
    }

    const params: Record<string, string | number> = {};

    if (input.timeMin) {
      params["timeMin"] = input.timeMin;
    } else {
      params["timeMin"] = new Date().toISOString();
    }

    if (input.timeMax) {
      params["timeMax"] = input.timeMax;
    }

    if (input.maxResults) {
      params["maxResults"] = input.maxResults;
    }

    if (input.pageToken) {
      params["pageToken"] = input.pageToken;
    }

    if (input.orderBy) {
      params["orderBy"] = input.orderBy;
    }

    if (input.q) {
      params["q"] = input.q;
    }

    if (input.singleEvents !== undefined) {
      params["singleEvents"] = input.singleEvents ? "true" : "false";
    }

    if (input.timeZone) {
      params["timeZone"] = input.timeZone;
    }

    const response = await nango.get({
      endpoint: `calendar/v3/calendars/${encodeURIComponent(input.calendarId)}/events`,
      params,
    });

    if (response.status !== 200) {
      console.error("Google Calendar API Error:", response.status, response.data);
      return {
        error: {
          status: response.status,
          message:
            `Google Calendar API Error: Failed to list events: Status Code ${response.status}`,
        },
      };
    }

    return response.data;
  } catch (error: any) {
    console.error("Error listing Google Calendar events:", error);
    const status = error?.response?.status || 500;
    const message = error?.response?.data?.error?.message ||
      error?.message ||
      "An unknown error occurred while listing Google Calendar events.";
    return { error: { status, message } };
  }
}
