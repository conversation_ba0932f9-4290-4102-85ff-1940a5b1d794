import type {
  GoogleCalendarEventDeleteInput,
  NangoAction,
} from '../../models';

type NangoError = { error: { status: number; message: string } };

export default async function deleteEvent(
  nango: NangoAction,
  input: GoogleCalendarEventDeleteInput
): Promise<null | NangoError> {
  const { calendarId, eventId, sendUpdates } = input;

  const endpoint = `/calendar/v3/calendars/${encodeURIComponent(calendarId)}/events/${encodeURIComponent(
    eventId
  )}`;

  const queryParams: Record<string, string> = {};
  if (sendUpdates !== undefined) {
    queryParams['sendUpdates'] = sendUpdates;
  }

  try {
    const response = await nango.proxy({
      method: 'DELETE',
      endpoint: endpoint,
      providerConfigKey: 'google-calendar',
      connectionId: nango.connectionId,
      params: queryParams,
      retries: 3,
    });

    return response.data;
  } catch (error: any) {
    const status = error.response?.status ?? 500;
    const message =
      error.response?.data?.error?.message ||
      error.message ||
      'Failed to delete Google Calendar event';

    console.error('Error deleting event:', error.response?.data || error);

    return { error: { status: status, message: message } };
  }
}
