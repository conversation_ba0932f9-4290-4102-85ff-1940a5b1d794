integrations:
    google-drive:
        actions:
            list-documents:
                endpoint: GET /list-documents
                description: Lists documents in Google Drive with optional filtering by folder ID and document type.
                input: ListDocumentsInput
                output: GoogleDriveDocumentList
                scopes:
                  - https://www.googleapis.com/auth/drive.readonly
            list-root-folders:
                endpoint: GET /list-root-folders
                description: Lists folders at the root level of Google Drive.
                output: GoogleDriveFolderList
                scopes:
                  - https://www.googleapis.com/auth/drive.readonly

models:
    GoogleDriveDocument:
        id: string
        name: string
        mimeType: string
        webViewLink: string
        modifiedTime: string
        createdTime: string
        parents: string[]
        size: string
    GoogleDriveFolder:
        id: string
        name: string
        mimeType: string
        webViewLink: string
        modifiedTime: string
        createdTime: string
        parents: string[]
    ListDocumentsInput:
        folderId?: string
        mimeType?: string
        pageSize?: number
        pageToken?: string
        orderBy?: string
    GoogleDriveDocumentList:
        documents: GoogleDriveDocument[]
        nextPageToken?: string
    GoogleDriveFolderList:
        folders: GoogleDriveFolder[]
        nextPageToken?: string
