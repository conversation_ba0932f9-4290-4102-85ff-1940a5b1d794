import type {
  NangoAction,
  SlackListChannelsInput,
  SlackChannelList,
  SlackChannel,
} from '../../models';

interface SlackConversation {
  id: string;
  name: string;
  is_channel: boolean;
  is_group: boolean;
  is_im: boolean;
  is_mpim: boolean;
  is_private: boolean;
  is_member: boolean;
  num_members?: number;
}

interface SlackConversationsListResponse {
  ok: boolean;
  channels: SlackConversation[];
  response_metadata?: {
    next_cursor?: string;
  };
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackListChannelsInput | undefined
): Promise<SlackChannelList | ErrorResponse> {
  const currentInput = input || {};
  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const params: { [key: string]: string | number } = {};
    if (currentInput.types !== undefined) {
      params['types'] = currentInput.types;
    }
    if (currentInput.limit !== undefined) {
      params['limit'] = currentInput.limit;
    }
    if (currentInput.cursor !== undefined) {
      params['cursor'] = currentInput.cursor;
    }

    const config = {
      method: 'GET' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'conversations.list',
      params: params,
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
      retries: 3,
    };

    const response = await nango.proxy<SlackConversationsListResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error listing channels'}`;
      await nango.log(errorMessage);
      const status = response.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    const channels: SlackChannel[] = response.data.channels.map((channel: SlackConversation) => ({
      id: channel.id,
      name: channel.name,
      is_private: channel.is_private || channel.is_group || channel.is_mpim,
      is_member: channel.is_member,
      ...(channel.num_members !== undefined && { num_members: channel.num_members }),
    }));

    const result: SlackChannelList = {
      channels: channels,
    };

    if (response.data.response_metadata?.next_cursor) {
      result.next_cursor = response.data.response_metadata.next_cursor;
    }

    return result;
  } catch (error: any) {
    await nango.log(`Error listing Slack channels: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error listing Slack channels';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}
