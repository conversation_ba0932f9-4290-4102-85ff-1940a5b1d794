import { useEffect, useState } from 'react';
import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import { useConnections } from 'hooks/useConnections';
import { connectProvider, deleteConnection, primeNango } from 'lib/nango';
import { PROVIDER_CONFIGS } from '../../config/integrationUI';
import { CheckSquare, Loader2, Plus } from 'lucide-react';

interface ConnectionProps {
  provider: string;
  showBackButton?: boolean;
  onBack?: () => void;
  onConnected?: () => void;
  onDisconnected?: () => void;
}

function Connection({
  provider,
  showBackButton,
  onBack,
  onConnected,
  onDisconnected,
}: ConnectionProps) {
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const { connections } = useConnections();
  const hasConnection = connections.some(conn => conn.providerKey === provider);
  const connection = connections.find(c => c.providerKey === provider);
  const config = PROVIDER_CONFIGS[provider as keyof typeof PROVIDER_CONFIGS];

  useEffect(() => {
    primeNango();
  }, []);

  // Monitor connection status changes
  useEffect(() => {
    if (hasConnection && isConnecting) {
      setIsConnecting(false);
      onConnected?.();
    }
  }, [hasConnection, isConnecting, onConnected]);

  if (!config) return null;

  const Icon = config.icon;
  const headerColor = config.headerColor || 'bg-indigo-600';

  const handleConnect = async () => {
    setIsConnecting(true);
    const [error] = await connectProvider(provider);
    if (error) {
      setIsConnecting(false);
    }
    onConnected?.();
  };

  const handleDisconnect = async () => {
    if (!connection) return;
    setIsDisconnecting(true);
    try {
      await deleteConnection(connection.id, provider);
    } finally {
      setTimeout(() => {
        setIsDisconnecting(false);
        onDisconnected?.();
      }, 600);
    }
  };

  return (
    <div className="flex flex-col min-h-[400px] bg-white dark:bg-gray-800 pb-12">
      {/* Header Banner */}
      <div className={clsx('relative h-24 rounded-t-lg', headerColor)}>
        <div className="absolute -bottom-3 left-6 flex items-center gap-4">
          <div
            className={clsx('p-4 bg-white rounded-lg shadow-md', {
              'dark:bg-black': config.invertInModal,
            })}
          >
            <Icon className="w-14 h-14" />
          </div>
          <div className="text-white -mt-6">
            <h2 className="text-2xl font-semibold">{config.name}</h2>
            <p className="text-sm text-white/80 mt-1">{config.title}</p>
          </div>
        </div>
        <ConnectButton
          hasConnection={hasConnection}
          isConnecting={isConnecting}
          handleConnect={handleConnect}
        />
      </div>

      {/* Content */}
      <div className="mt-10 px-6 flex-1">
        <Tab.Group>
          <Tab.List className="flex gap-6 border-b border-gray-200 dark:border-gray-700">
            <Tab
              className={({ selected }) =>
                clsx(
                  'px-1 py-3 text-sm font-medium border-b-2 focus:outline-none',
                  selected
                    ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                )
              }
            >
              Overview
            </Tab>
            <Tab
              className={({ selected }) =>
                clsx(
                  'px-1 py-3 text-sm font-medium border-b-2 focus:outline-none',
                  selected
                    ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                )
              }
            >
              Configuration
            </Tab>
          </Tab.List>

          <Tab.Panels>
            <Tab.Panel className="py-6">
              <div className="space-y-6">
                <p className="text-gray-600 dark:text-gray-300">{config.description}</p>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">Features:</h3>
                  <ul className="space-y-2">
                    {config.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300"
                      >
                        <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </Tab.Panel>

            <Tab.Panel className="py-6">
              <div className="space-y-6">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {!hasConnection
                    ? `Connect to let agents utilize ${config.name}`
                    : 'Disconnecting will deactivate any agents using this connection'}
                </p>

                {!hasConnection && (
                  <div>
                    <button
                      onClick={handleConnect}
                      className="px-8 py-3 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-500 rounded-sm"
                    >
                      Connect
                    </button>
                  </div>
                )}

                {hasConnection && (
                  <div>
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={clsx(
                        'px-6 py-2.5 text-sm font-medium border rounded-sm inline-flex items-center gap-2',
                        isDisconnecting
                          ? 'border-gray-300 text-gray-400 cursor-not-allowed dark:border-gray-600 dark:text-gray-500'
                          : 'border-red-700 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300'
                      )}
                    >
                      {isDisconnecting && <Loader2 className="w-4 h-4 animate-spin" />}
                      {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
                    </button>
                  </div>
                )}
              </div>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>

      {showBackButton && (
        <div className="absolute bottom-1 -left-0.5 mt-auto px-6 py-4 border-gray-200 dark:border-gray-700">
          <button
            onClick={onBack}
            className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ← Back to connections
          </button>
        </div>
      )}
    </div>
  );
}

function ConnectButton({
  hasConnection,
  isConnecting,
  handleConnect,
}: {
  hasConnection: boolean;
  isConnecting: boolean;
  handleConnect: () => void;
  mobile?: boolean;
}) {
  return (
    <button
      onClick={hasConnection ? undefined : handleConnect}
      disabled={hasConnection || isConnecting}
      className={clsx(
        'absolute right-6 top-1/2 -translate-y-1/2 text-sm font-medium transition-colors sm:px-8 sm:py-3.5 px-3 py-3 sm:rounded-sm rounded-lg sm: ',
        {
          'bg-white/20 text-white cursor-default': hasConnection,
          'bg-white/80 text-gray-500 cursor-wait': isConnecting,
          'bg-white text-gray-900 hover:bg-gray-50': !hasConnection && !isConnecting,
        }
      )}
    >
      <span className="hidden sm:inline">
        {hasConnection ? 'Connected' : isConnecting ? 'Connecting...' : 'Connect'}
      </span>
      <span className="sm:hidden">
        {hasConnection ? (
          <CheckSquare className="w-5 h-5" />
        ) : isConnecting ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <Plus className="w-5 h-5" />
        )}
      </span>
    </button>
  );
}

export { Connection };
