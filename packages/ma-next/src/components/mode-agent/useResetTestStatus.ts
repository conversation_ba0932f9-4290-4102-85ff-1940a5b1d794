import { supabase } from 'lib/supabase';
import { useEffect } from 'react';

/**
 * Hook to reset test status when clicking outside of the agent panel
 * @param agentPanelRef - Reference to the agent panel element
 * @param resetTest - Function to reset the test
 * @param testStatus - Current test status
 */
function useResetTestStatus(
  agentPanelRef: React.RefObject<HTMLDivElement | null>,
  taskflowId: string,
  setTaskflow: React.Dispatch<React.SetStateAction<any>>,
  testStatus?: string
) {
  useEffect(() => {
    if (!testStatus || testStatus === 'idle') {
      return;
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (agentPanelRef.current && !agentPanelRef.current.contains(event.target as Node)) {
        supabase.from('taskflows').update({ testExecutionId: null }).eq('id', taskflowId);

        setTaskflow((prevTaskflow: any) => ({ ...prevTaskflow, testExecutionId: null }));
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [agentPanelRef, testStatus, taskflowId]);
}

export { useResetTestStatus };
