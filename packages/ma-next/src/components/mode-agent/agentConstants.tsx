import {
  SlackIcon,
  NotionIcon,
  LinkedInIcon,
  GmailIcon,
  GoogleCalendarIcon,
  ZoomIcon,
  ShopifyIcon,
  GithubIcon,
} from 'components/icons/providers';

// Define provider types for type safety
type ProviderType =
  | 'zoom'
  | 'slack'
  | 'shopify'
  | 'notion'
  | 'linkedin'
  | 'google-mail'
  | 'google-calendar'
  | 'github';

// Icon lookup map for providers
const ICON_LOOKUP: Record<ProviderType, JSX.Element> = {
  zoom: <ZoomIcon />,
  slack: <SlackIcon />,
  shopify: <ShopifyIcon />,
  notion: <NotionIcon />,
  linkedin: <LinkedInIcon />,
  'google-mail': <GmailIcon />,
  'google-calendar': <GoogleCalendarIcon />,
  github: <GithubIcon />,
};

// Label lookup maps for providers
const SYNC_KEY_LABELS: Record<ProviderType, Record<string, string>> = {
  zoom: {
    meetings: 'Meeting synced',
    recordings: 'Recording synced',
  },
  slack: {
    messages: 'Message synced',
    channels: 'Channel synced',
  },
  shopify: {
    products: 'Product synced',
    orders: 'Order synced',
  },
  notion: {
    pages: 'Page synced',
    databases: 'Database synced',
  },
  linkedin: {
    posts: 'Post synced',
    connections: 'Connection synced',
  },
  'google-mail': {
    labels: 'Label added',
    'emails-fork': 'Receive email',
  },
  'google-calendar': {
    events: 'Event synced',
    calendars: 'Calendar synced',
  },
  github: {
    repos: 'Repository synced',
    issues: 'Issue synced',
  },
};

const ACTION_KEY_LABELS: Record<ProviderType, Record<string, string>> = {
  zoom: {
    'create-meeting': 'Create meeting',
    'join-meeting': 'Join meeting',
  },
  slack: {
    'send-message': 'Send message',
    'create-channel': 'Create channel',
  },
  shopify: {
    'create-product': 'Create product',
    'update-inventory': 'Update inventory',
  },
  notion: {
    'fetch-rich-page': 'Read page',
    'update-database': 'Update database',
  },
  linkedin: {
    'create-post': 'Create post',
    'send-connection': 'Send connection',
  },
  'google-mail': {
    'send-email': 'Send email',
    'compose-draft': 'Compose draft',
    'compose-draft-reply': 'Draft reply',
  },
  'google-calendar': {
    'create-event': 'Create event',
    'update-event': 'Update event',
  },
  github: {
    'create-issue': 'Create issue',
    'create-pr': 'Create PR',
  },
};

export type { ProviderType };
export { ICON_LOOKUP, SYNC_KEY_LABELS, ACTION_KEY_LABELS };
