import { Modal } from '../Modal';
import { Connection } from 'components/setup/Connection';

interface ConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  provider: string;
  onConnected: () => void;
  onDisconnected: () => void;
}

function ConnectionModal({
  isOpen,
  onClose,
  onConnected,
  onDisconnected,
  provider,
}: ConnectionModalProps) {
  return (
    <Modal isOpen={isOpen} onClose={onClose} hideHeader mediumSize>
      <Connection provider={provider} onConnected={onConnected} onDisconnected={onDisconnected} />
    </Modal>
  );
}

export { ConnectionModal };
