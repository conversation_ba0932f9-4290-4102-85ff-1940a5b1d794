import { GmailMessageDisplay } from './rich-results/GmailMessageDisplay';
import { GmailSingleMessageDisplay } from './rich-results/GmailSingleMessageDisplay';
import { GmailDraftOutputDisplay } from './rich-results/GmailDraftOutputDisplay';
import { GmailSendEmailOutputDisplay } from './rich-results/GmailSendEmailOutputDisplay';
import { GoogleCalendarEventDisplay } from './rich-results/GoogleCalendarEventDisplay';
import { GoogleCalendarEventOutputDisplay } from './rich-results/GoogleCalendarEventOutputDisplay';
import { GoogleCalendarListDisplay } from './rich-results/GoogleCalendarListDisplay';
import { GoogleDriveDocumentListDisplay } from './rich-results/GoogleDriveDocumentListDisplay';
import { GoogleDriveFolderListDisplay } from './rich-results/GoogleDriveFolderListDisplay';
import { FolderContentDisplay } from './rich-results/FolderContentDisplay';
import { SlackChannelListDisplay } from './rich-results/SlackChannelListDisplay';
import { SlackMessageListDisplay } from './rich-results/SlackMessageListDisplay';
import { SlackSendMessageResultDisplay } from './rich-results/SlackSendMessageResultDisplay';
import { SlackUserInfoDisplay } from './rich-results/SlackUserInfoDisplay';
import { SlackUpdateMessageOutputDisplay } from './rich-results/SlackUpdateMessageOutputDisplay';
import { DropboxFolderDisplay } from './rich-results/DropboxFolderDisplay';
import { DropboxFileDisplay } from './rich-results/DropboxFileDisplay';
import { DropboxFileListDisplay } from './rich-results/DropboxFileListDisplay';
import { DropboxSearchResultDisplay } from './rich-results/DropboxSearchResultDisplay';
import { NotionPageOrDatabaseDisplay } from './rich-results/NotionPageOrDatabaseDisplay';
import { NotionSearchResultDisplay } from './rich-results/NotionSearchResultDisplay';
import { XSocialUserProfileDisplay } from './rich-results/XSocialUserProfileDisplay';
import { XSocialPostOutputDisplay } from './rich-results/XSocialPostOutputDisplay';
import { LinkedInUserProfileDisplay } from './rich-results/LinkedInUserProfileDisplay';

// Google Docs
import { GoogleDocsCreateDocumentDisplay } from './rich-results/GoogleDocsCreateDocumentDisplay';
import { GoogleDocsGetDocumentDisplay } from './rich-results/GoogleDocsGetDocumentDisplay';
import { GoogleDocsFetchDocumentDisplay } from './rich-results/GoogleDocsFetchDocumentDisplay';
import { GoogleDocsUpdateDocumentDisplay } from './rich-results/GoogleDocsUpdateDocumentDisplay';

// Google Sheets
import { GoogleSheetCreateSheetDisplay } from './rich-results/GoogleSheetCreateSheetDisplay';
import { GoogleSheetFetchSpreadsheetDisplay } from './rich-results/GoogleSheetFetchSpreadsheetDisplay';
import { GoogleSheetUpdateSheetDisplay } from './rich-results/GoogleSheetUpdateSheetDisplay';

// Google Drive
import { GoogleDriveFetchDocumentDisplay } from './rich-results/GoogleDriveFetchDocumentDisplay';
import { GoogleDriveFetchGoogleDocDisplay } from './rich-results/GoogleDriveFetchGoogleDocDisplay';
import { GoogleDriveFetchGoogleSheetDisplay } from './rich-results/GoogleDriveFetchGoogleSheetDisplay';
import { GoogleDriveUploadDocumentDisplay } from './rich-results/GoogleDriveUploadDocumentDisplay';

export const RICH_RESULT_COMPONENTS: Record<string, React.FC<{ output: any }>> = {
  // Google Mail
  'google-mail:list-messages': GmailMessageDisplay,
  'google-mail:get-message': GmailSingleMessageDisplay,
  'google-mail:compose-draft': GmailDraftOutputDisplay,
  'google-mail:send-email': GmailSendEmailOutputDisplay,

  // Google Calendar
  'google-calendar:list-events': GoogleCalendarEventDisplay,
  'google-calendar:list-calendars': GoogleCalendarListDisplay,
  'google-calendar:create-event': GoogleCalendarEventOutputDisplay,
  'google-calendar:update-event': GoogleCalendarEventOutputDisplay,

  // Google Docs
  'google-docs:create-document': GoogleDocsCreateDocumentDisplay,
  'google-docs:fetch-document': GoogleDocsFetchDocumentDisplay,
  'google-docs:get-document': GoogleDocsGetDocumentDisplay,
  'google-docs:update-document': GoogleDocsUpdateDocumentDisplay,

  // Google Sheets
  'google-sheet:create-sheet': GoogleSheetCreateSheetDisplay,
  'google-sheet:fetch-spreadsheet': GoogleSheetFetchSpreadsheetDisplay,
  'google-sheet:update-sheet': GoogleSheetUpdateSheetDisplay,

  // Google Drive
  'google-drive:list-documents': GoogleDriveDocumentListDisplay,
  'google-drive:list-root-folders': GoogleDriveFolderListDisplay,
  'google-drive:folder-content': FolderContentDisplay,
  'google-drive:fetch-document': GoogleDriveFetchDocumentDisplay,
  'google-drive:fetch-google-doc': GoogleDriveFetchGoogleDocDisplay,
  'google-drive:fetch-google-sheet': GoogleDriveFetchGoogleSheetDisplay,
  'google-drive:upload-document': GoogleDriveUploadDocumentDisplay,

  // Slack
  'slack:list-channels': SlackChannelListDisplay,
  'slack:get-channel-history': SlackMessageListDisplay,
  'slack:send_message_as_user': SlackSendMessageResultDisplay,
  'slack:get-user-info': SlackUserInfoDisplay,
  'slack:update_message_as_user': SlackUpdateMessageOutputDisplay,

  // Dropbox
  'dropbox:create-folder': DropboxFolderDisplay,
  'dropbox:get-file': DropboxFileDisplay,
  'dropbox:list-files': DropboxFileListDisplay,
  'dropbox:search-files': DropboxSearchResultDisplay,

  // Notion
  'notion:get-page': NotionPageOrDatabaseDisplay,
  'notion:create-page': NotionPageOrDatabaseDisplay,
  'notion:update-page': NotionPageOrDatabaseDisplay,
  'notion:search': NotionSearchResultDisplay,

  // Social Media
  'x-social:get-user-profile': XSocialUserProfileDisplay,
  'x-social:send-post': XSocialPostOutputDisplay,
  'linkedin:get-user-profile': LinkedInUserProfileDisplay,
};

interface RichResultDisplayProps {
  result?: any;
  providerKey: string;
  actionKey: string;
}

/**
 * Component that displays rich UI for specific types of data
 * Currently supports:
 * - google-mail: list-messages, get-message, compose-draft, send-email
 * - google-calendar: list-events, list-calendars, create-event, update-event
 * - google-docs: create-document, fetch-document, get-document, update-document
 * - google-sheet: create-sheet, fetch-spreadsheet, update-sheet
 * - google-drive: list-documents, list-root-folders, folder-content, fetch-document,
 *   fetch-google-doc, fetch-google-sheet, upload-document
 * - slack: list-channels, get-channel-history, send_message_as_user, get-user-info, update_message_as_user
 * - dropbox: create-folder, get-file, list-files, search-files
 * - notion: get-page, create-page, update-page, search
 * - x-social: get-user-profile, send-post
 * - linkedin: get-user-profile
 */
function RichResultDisplay({ result, providerKey, actionKey }: RichResultDisplayProps) {
  const key = `${providerKey}:${actionKey}`;
  const DisplayComponent = RICH_RESULT_COMPONENTS[key];
  if (!DisplayComponent) return null;

  return (
    <div className="mb-8 -mt-2 rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden">
      <DisplayComponent output={result} />
    </div>
  );
}

// Static method to check if a provider/action combination has a rich display
RichResultDisplay.canDisplay = (providerKey: string, actionKey: string): boolean => {
  return Boolean(RICH_RESULT_COMPONENTS[`${providerKey}:${actionKey}`]);
};

export { RichResultDisplay };
