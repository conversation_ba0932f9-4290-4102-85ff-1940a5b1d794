import { GmailDraftDisplay } from './rich-parameters/GmailDraftDisplay';
import { GoogleCalendarEventDisplay } from './rich-parameters/GoogleCalendarEventDisplay';
import { TwitterPostDisplay } from './rich-parameters/TwitterPostDisplay';
import { SlackListChannelsDisplay } from './rich-parameters/SlackListChannelsDisplay';
import { SlackSendMessageDisplay } from './rich-parameters/SlackSendMessageDisplay';
import { XSocialPostDisplay } from './rich-parameters/XSocialPostDisplay';
import { NotionCreatePageDisplay } from './rich-parameters/NotionCreatePageDisplay';
import { NotionSearchDisplay } from './rich-parameters/NotionSearchDisplay';

const RICH_PARAM_COMPONENTS: Record<string, React.FC<{ parameters: any }>> = {
  'google-mail:compose-draft': GmailDraftDisplay,
  'google-mail:send-email': GmailDraftDisplay,
  'google-calendar:create-event': GoogleCalendarEventDisplay,
  'twitter-v2:create-post': TwitterPostDisplay,
  'slack:list-channels': SlackListChannelsDisplay,
  'slack:send_message_as_user': SlackSendMessageDisplay,
  'x-social:send-post': XSocialPostDisplay,
  'notion:create-page': NotionCreatePageDisplay,
  'notion:search': NotionSearchDisplay,
};

interface RichParameterDisplayProps {
  providerKey: string;
  actionKey: string;
  actionParameters: Record<string, any>;
}

/**
 * Component that displays rich UI for specific types of parameters
 * Currently supports:
 * - google-mail/compose-draft, send-email
 * - google-calendar/create-event
 * - twitter-v2/create-post
 * - slack/list-channels
 * - slack/search-messages
 * - slack/send_message_as_user
 * - x-social/send-post
 * - notion/create-page, search
 */
function RichParameterDisplay({
  providerKey,
  actionKey,
  actionParameters,
}: RichParameterDisplayProps) {
  // Determine if we have a rich parameter display component
  const key = `${providerKey}:${actionKey}`;
  const DisplayComponent = RICH_PARAM_COMPONENTS[key];
  if (!DisplayComponent) return null;

  return (
    <div>
      <DisplayComponent parameters={actionParameters} />
    </div>
  );
}

// Static method to check if a provider/action combination has a rich display
RichParameterDisplay.canDisplay = (providerKey: string, actionKey: string): boolean => {
  return Boolean(RICH_PARAM_COMPONENTS[`${providerKey}:${actionKey}`]);
};

export { RichParameterDisplay };
