import { User, AlignLeft } from 'lucide-react';
import { GmailDraftInput } from 'src/config/nangoModels';

/**
 * Renders a rich display of Gmail draft parameters
 */
function GmailDraftDisplay({ parameters }: { parameters: GmailDraftInput }) {
  // Extract parameters
  const recipient = parameters?.recipient || '';
  const subject = parameters?.subject || '';
  const body = parameters?.body || '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      {/* Email content */}
      <div className="p-5 space-y-4">
        {/* To field */}
        <div className="flex items-center mb-3">
          <User className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 flex-shrink-0" />
          <div className="flex-1 flex">
            <div className="text-sm min-w-20 font-medium text-gray-500 dark:text-gray-400 mb-1">
              To:
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-200">{recipient}</div>
          </div>
        </div>

        {/* Subject field */}
        <div className="flex items-center mb-3">
          <AlignLeft className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 flex-shrink-0" />
          <div className="flex-1 flex">
            <div className="text-sm min-w-20 font-medium text-gray-500 dark:text-gray-400 mb-1">
              Subject:
            </div>
            <div className="text-sm font-medium text-gray-800 dark:text-gray-200">{subject}</div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 dark:border-gray-700 my-3"></div>

        {/* Body */}
        <div className="whitespace-pre-line text-sm text-gray-800 dark:text-gray-200 p-2 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
          {body}
        </div>
      </div>
    </div>
  );
}

export { GmailDraftDisplay };
