import React from 'react';
import { MessageSquare, Hash, Lock } from 'lucide-react';
import { SlackChannelList } from 'src/config/nangoModels';

interface SlackChannelListDisplayProps {
  output: SlackChannelList;
}

/**
 * Renders a rich display of Slack channel list results
 */
function SlackChannelListDisplay({ output }: SlackChannelListDisplayProps) {
  const data = output;
  const channels = data?.channels || [];
  const nextCursor = data?.next_cursor;

  // If no channels, show empty state
  if (!channels || channels.length === 0) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No channels found</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Channels ({channels.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {channels.map((channel, index) => (
          <div
            key={channel.id || index}
            className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <div className="flex items-center">
              {channel.is_private ? (
                <Lock className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
              ) : (
                <Hash className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
              )}
              <div className="min-w-0 flex-1">
                <div className="flex justify-between items-baseline">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {channel.name}
                  </h4>
                  {channel.num_members !== undefined && (
                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-3 flex-shrink-0">
                      {channel.num_members} members
                    </span>
                  )}
                </div>
                <div className="flex mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                    {channel.is_member ? (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                        Member
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                        Not a member
                      </span>
                    )}
                  </span>
                  {channel.is_private && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                      Private
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {nextCursor && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More channels available. Use cursor for pagination.
          </p>
        </div>
      )}
    </div>
  );
}

export { SlackChannelListDisplay };
