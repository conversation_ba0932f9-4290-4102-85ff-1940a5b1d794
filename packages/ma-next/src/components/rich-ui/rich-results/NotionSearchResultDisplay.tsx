import React from 'react';
import { Search, FileText, Database, Calendar, Link, Archive } from 'lucide-react';
import { format } from 'date-fns';
import { NotionSearchOutput, NotionPageOrDatabase, NotionRichText } from 'src/config/nangoModels';

type NotionSearchResultDisplayProps = {
  output: NotionSearchOutput;
};

/**
 * Renders a rich display of Notion search results
 */
function NotionSearchResultDisplay({ output }: NotionSearchResultDisplayProps) {
  if (!output || !output.results) {
    return (
      <div className="p-6 text-center">
        <Search className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No search results available</p>
      </div>
    );
  }

  const results = output.results;
  const hasMore = output.has_more;
  const nextCursor = output.next_cursor;

  // If no results, show empty state
  if (results.length === 0) {
    return (
      <div className="p-6 text-center">
        <Search className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No Notion pages or databases found</p>
      </div>
    );
  }

  // Helper function to extract text from Notion rich text array
  const getTitleText = (richTextArray?: NotionRichText[]): string => {
    if (!richTextArray || richTextArray.length === 0) return 'Untitled';

    return richTextArray.map(rt => rt.plain_text || rt.text?.content || '').join('');
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Search className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Notion Search Results ({results.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {results.map((result, index) => {
          // Determine if it's a page or database
          const isPage = result.object === 'page';
          const isDatabase = result.object === 'database';
          const title = getTitleText(result.title);
          const isArchived = result.archived || result.in_trash;

          // Format date if available
          let lastEditedDate = '';
          if (result.last_edited_time) {
            try {
              lastEditedDate = format(new Date(result.last_edited_time), 'MMM d, yyyy');
            } catch (e) {
              lastEditedDate = result.last_edited_time;
            }
          }

          return (
            <div
              key={result.id || index}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="w-9 h-9 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    {isPage ? (
                      <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    ) : (
                      <Database className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    )}
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {title}
                    </h4>
                    {isArchived && (
                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                        <Archive className="w-3 h-3 mr-1" />
                        Archived
                      </span>
                    )}
                  </div>

                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span className="capitalize">
                      {isPage ? 'Page' : isDatabase ? 'Database' : result.object}
                    </span>
                    {lastEditedDate && (
                      <>
                        <span className="mx-1">•</span>
                        <Calendar className="w-3 h-3 mr-1" />
                        <span>Last edited: {lastEditedDate}</span>
                      </>
                    )}
                  </div>

                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                    ID: {result.id}
                  </p>

                  {/* URL link */}
                  {result.url && (
                    <div className="mt-2">
                      <a
                        href={result.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        <Link className="w-3 h-3 mr-1" />
                        Open in Notion
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More results available. {nextCursor ? 'Use cursor for pagination.' : ''}
          </p>
        </div>
      )}
    </div>
  );
}

export { NotionSearchResultDisplay };
