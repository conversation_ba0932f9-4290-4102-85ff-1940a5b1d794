import { useState, useEffect } from 'react';
import { PROVIDER_CONFIGS } from 'src/config/integrationUI';
import { ACTION_KEY_LABELS, ProviderType } from 'components/mode-agent/agentConstants';
import { ActionCallArgs, ActionCallResult } from 'chat/protocol/tools';
import { ActionHeader } from './ActionHeader';
import { ActionParameters } from './ActionParameters';
import { ActionResult } from './ActionResult';
import { ActionButtons } from './ActionButtons';
import clsx from 'clsx';

type ActionCardProps = {
  actionConfig: ActionCallArgs;
  toolCallId: string;
  hideHeader?: boolean;
  disabled?: boolean;
  result?: ActionCallResult;
  isCancelled?: boolean;
  hideButtons?: boolean;
  bulk?: boolean; // When true, styles the card for bulk confirmation view
  autoApproved?: boolean; // When true, shows auto-approved UI
};

function ActionCard({
  actionConfig,
  toolCallId,
  hideHeader,
  disabled,
  result,
  isCancelled,
  hideButtons,
  bulk = false,
  autoApproved = false,
}: ActionCardProps) {
  const isComplete: boolean = !!result || !!isCancelled;

  const [showParameters, setShowParameters] = useState(!isComplete);

  useEffect(() => {
    if (isComplete) {
      setShowParameters(false);
    }
  }, [isComplete]);

  const { providerKey, actionKey, actionParameters } = actionConfig;
  if (!providerKey || !actionKey) {
    return null;
  }

  const providerInfo = PROVIDER_CONFIGS[providerKey as keyof typeof PROVIDER_CONFIGS];

  const actionDisplayName =
    ACTION_KEY_LABELS[providerKey as ProviderType]?.[actionKey] ||
    actionKey
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

  return (
    <div
      className={clsx('my-6', {
        'border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white/90 dark:bg-gray-800/90':
          bulk && !autoApproved,
        'border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-white/90 dark:bg-gray-800/90':
          bulk && autoApproved,
        'rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 bg-white/90 dark:bg-gray-800/90':
          !bulk && !autoApproved,
        'rounded-lg p-6 shadow-sm border border-blue-200 dark:border-blue-700 bg-white/90 dark:bg-gray-800/90':
          !bulk && autoApproved,
        'opacity-50': disabled,
      })}
    >
      <ActionHeader
        hideHeader={hideHeader}
        providerInfo={providerInfo}
        actionDisplayName={actionDisplayName}
        actionKey={actionKey}
        autoApproved={autoApproved}
      />

      {/* Only show parameters outside when there's no result */}
      {!isComplete && (
        <ActionParameters
          actionParameters={actionParameters}
          isComplete={isComplete}
          showParameters={showParameters}
          setShowParameters={setShowParameters}
          providerKey={providerKey}
          actionKey={actionKey}
        />
      )}

      <ActionResult
        result={result}
        isCancelled={isCancelled}
        actionParameters={actionParameters}
        providerKey={providerKey}
        actionKey={actionKey}
      />

      {!hideButtons && (
        <ActionButtons
          toolCallId={toolCallId}
          disabled={disabled}
          isComplete={isComplete}
          providerKey={providerKey}
          actionKey={actionKey}
          autoApproved={autoApproved}
        />
      )}
    </div>
  );
}

export { ActionCard };
