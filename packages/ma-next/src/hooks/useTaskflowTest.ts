import { useCallback } from 'react';
import { supabase } from 'lib/supabase';
import { invokeFunction } from 'utils/invokeFunction';

/**
 * Custom hook to handle testing a taskflow
 * @param taskflowId The ID of the taskflow to test
 * @param testExecutionId The current test execution ID
 * @returns Functions to start and reset tests
 */
function useTaskflowTest(taskflowId: string, testExecutionId: string | null) {
  // Function to start a test
  const startTest = useCallback(async () => {
    try {
      // Get the trigger ID from the taskflow
      const { data: taskflow } = await supabase
        .from('taskflows')
        .select('schema')
        .eq('id', taskflowId)
        .single();

      if (!taskflow?.schema?.triggers?.[0]?.id) {
        throw new Error('No trigger ID available');
      }

      const triggerId = taskflow.schema.triggers[0].id;

      // Call the test workflow API with the triggerId
      const { data, error } = await invokeFunction('test-workflow', {
        body: {
          triggerId,
        },
      });

      if (error || !data?.executionId) {
        throw new Error(error?.message || 'Failed to start test execution');
      }

      return data.executionId;
    } catch (error) {
      console.error('Failed to start test:', error);
      return null;
    }
  }, [taskflowId]);

  // Function to reset the test
  const resetTest = useCallback(async () => {
    try {
      await supabase.from('taskflows').update({ testExecutionId: null }).eq('id', taskflowId);

      return true;
    } catch (error) {
      console.error('Failed to reset test:', error);
      return false;
    }
  }, [taskflowId]);

  return {
    startTest,
    resetTest,
  };
}

export { useTaskflowTest };
