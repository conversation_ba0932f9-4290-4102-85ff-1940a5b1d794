import { useSyncExternalStore } from 'react';

function createStore<T extends object>(initialState: T) {
  let state = initialState;
  const listeners = new Set<() => void>();

  return {
    getState: () => state,
    setState: (partial: Partial<T>) => {
      state = { ...state, ...partial };
      listeners.forEach(listener => listener());
    },
    subscribe: (listener: () => void) => {
      listeners.add(listener);
      return () => listeners.delete(listener);
    },
    useStoreState: <S = T>(selector: (state: T) => S = state => state as unknown as S) => {
      return useSyncExternalStore(
        listener => {
          listeners.add(listener);
          return () => listeners.delete(listener);
        },
        () => selector(state), // getSnapshot (client)
        () => selector(initialState) // getServerSnapshot (server)
      );
    },
  };
}

export { createStore };
