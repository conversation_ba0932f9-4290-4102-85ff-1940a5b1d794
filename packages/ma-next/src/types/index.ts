interface Step {
  id: string;
  text: string;
  completed: boolean;
  skippable?: boolean;
}

interface Example {
  input: string;
  output: string;
}

type TestStatus = 'idle' | 'simulating' | 'working' | 'success' | 'failed';
type TabType = 'brief' | 'examples';
type NodeStatus =
  | 'idle'
  | 'simulating'
  | 'processing'
  | 'success'
  | 'failed'
  | 'active'
  | 'inactive'
  | 'RUNNING'
  | 'PAUSED'
  | 'SUCCESS'
  | 'ERROR';

// Response types
type ResponseType = 'connectProvider' | 'fineTune' | 'userLocation';

interface SetupStepType {
  type: ResponseType | 'auth';
  completed?: boolean;
  skippable?: boolean;
  data?: StepData;
  provider?: string;
  advanced?: boolean;
}

interface StepData {
  text?: string;
  brief?: string;
  examples?: Example[];
  latitude?: number;
  longitude?: number;
}

interface NodeConnection {
  source: string;
  target: string;
}

interface Conversation {
  id: string;
  title: string | null;
  createdAt: string;
  updatedAt: string;
  userId: string;
  currentTaskflowId: string | null;
  agent: { id: string; active: boolean; triggers: { providerKey: string }[] } | null;
  mode: 'task' | 'agent';
}

export type {
  Conversation,
  Example,
  NodeConnection,
  NodeStatus,
  ResponseType,
  SetupStepType,
  Step,
  StepData,
  TabType,
  TestStatus,
};
