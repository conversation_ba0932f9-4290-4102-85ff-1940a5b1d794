{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"components/*": ["./src/components/*"], "providers/*": ["./src/providers/*"], "lib/*": ["./src/lib/*"], "app/*": ["./src/app/*"], "hooks/*": ["./src/hooks/*"], "intl/*": ["./src/intl/*"], "intl": ["./src/intl/index.ts"], "stores/*": ["./src/stores/*"], "types/*": ["./src/types/*"], "utils/*": ["./src/utils/*"], "constants/*": ["./src/constants/*"], "constants": ["./src/constants/index.ts"], "chat/*": ["./src/chat/*"], "chat": ["./src/chat/index.ts"], "src/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../../scripts/playgrounds/helpers/vercel.ts", "../../scripts/playgrounds/helpers/vercelConverter.ts"], "exclude": ["node_modules"]}