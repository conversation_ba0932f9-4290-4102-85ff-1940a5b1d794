import { Nango } from '@nangohq/node';
import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initServiceRoleSupabase } from '../_shared/supabase';

const nango = new Nango({
  secretKey: process.env.NANGO_SECRET_KEY || '',
});

if (!nango.secretKey) {
  throw new Error('Nango secret key not configured');
}

export default async function handler(req: Request) {
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'POST') {
      return validationError('Method not allowed');
    }

    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('No valid Authorization header');
    }
    const supabaseJwt = authHeader.split(' ')[1];

    const supabase = initServiceRoleSupabase();

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(supabaseJwt);

    if (error || !user) {
      throw new Error('Failed to get user from Supabase');
    }

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('firstName, lastName')
      .eq('id', user.id)
      .single();

    if (profileError) {
      throw new Error('Failed to get profile from Supabase');
    }

    const res = await nango.createConnectSession({
      end_user: {
        id: user.id, // Use Supabase user ID
        email: user?.email,
        display_name: `${profile?.firstName} ${profile?.lastName}`
          .trim()
          .concat(process.env.NETLIFY_DEV === 'true' ? ' (dev)' : ''),
      },
      // Optional: Add organization details if needed
      // organization: {
      //   id: "org-id",
      //   display_name: "Org Name",
      // },
      // allowed_integrations: process.env.NANGO_INTEGRATIONS?.split(",") || [],
    });

    // Return the session token
    return new Response(JSON.stringify({ token: res.data.token }), {
      headers: corsHeaders,
    });
  } catch (error) {
    return errorResponse(error);
  }
}
