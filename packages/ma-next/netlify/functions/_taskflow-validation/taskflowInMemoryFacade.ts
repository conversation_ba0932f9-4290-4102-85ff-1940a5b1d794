import { v4 as uuidv4 } from 'uuid';
import { SupabaseClient } from '@supabase/supabase-js';

type Profile = {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  preferences?: any | null;
};

type Conversation = {
  id: string;
  userId: string;
};

type Taskflow = {
  id: string;
  schema: any;
  conversationId: string;
  active: boolean;
};

type TaskflowExecution = {
  id: string;
  taskflowId: string;
  triggerData: Record<string, any>;
  context: Record<string, any>;
  result?: any;
  status?: string;
  startedAt: Date;
  updatedAt: Date;
  completedAt?: Date | null;
};

type Connection = {
  id: string;
  providerKey: string;
  userId: string;
};

type SyncTrigger = {
  id: string;
  taskflowId: string;
  providerKey: string;
  model: string;
  syncKey: string;
  condition: any;
  createdAt: Date;
};

export class TaskflowInMemoryFacade {
  private profiles = new Map<string, Profile>();
  private conversations = new Map<string, Conversation>();
  private taskflows = new Map<string, Taskflow>();
  private taskflowExecutions = new Map<string, TaskflowExecution>();
  private connections = new Map<string, Connection>();
  private syncTriggers = new Map<string, SyncTrigger>();

  constructor() {
    // Optionally initialize with some default data if needed
  }

  async getProfile(userId: string): Promise<{ data: Profile | null; error: null }> {
    const profile = this.profiles.get(userId) || null;
    return { data: profile, error: null };
  }

  async getActiveExecution(
    executionId: string
  ): Promise<{ data: TaskflowExecution | null; error: null }> {
    const execution = this.taskflowExecutions.get(executionId) || null;
    if (execution && !execution.completedAt) {
      return { data: execution, error: null };
    }
    return { data: null, error: null };
  }

  async getUserTaskflow(
    taskflowId: string,
    userId: string
  ): Promise<{
    data: { taskflowSchema: any; conversations: { id: string; userId: string }[] } | null;
    error: null;
  }> {
    const taskflow = this.taskflows.get(taskflowId);
    if (!taskflow) {
      return { data: null, error: null };
    }
    const conversation = this.conversations.get(taskflow.conversationId);
    if (!conversation || conversation.userId !== userId) {
      return { data: null, error: null };
    }
    return {
      data: {
        taskflowSchema: taskflow.schema,
        conversations: [{ id: conversation.id, userId: conversation.userId }],
      },
      error: null,
    };
  }

  async createExecution(
    taskflowId: string,
    triggerData: Record<string, any>
  ): Promise<{ data: { id: string }; error: null }> {
    const id = uuidv4();
    const now = new Date();
    const execution: TaskflowExecution = {
      id,
      taskflowId,
      triggerData,
      context: {},
      status: 'RUNNING',
      startedAt: now,
      updatedAt: now,
      completedAt: null,
    };
    this.taskflowExecutions.set(id, execution);
    return { data: { id }, error: null };
  }

  async updateExecution(
    executionId: string,
    updates: Partial<TaskflowExecution>
  ): Promise<{ data: TaskflowExecution | null; error: null }> {
    const execution = this.taskflowExecutions.get(executionId);
    if (!execution) {
      return { data: null, error: null };
    }
    const updatedExecution = { ...execution, ...updates, updatedAt: new Date() };
    this.taskflowExecutions.set(executionId, updatedExecution);
    return { data: updatedExecution, error: null };
  }

  async getConnection(
    providerKey: string,
    userId: string
  ): Promise<{ data: Connection | null; error: null }> {
    for (const connection of this.connections.values()) {
      if (connection.providerKey === providerKey && connection.userId === userId) {
        return { data: connection, error: null };
      }
    }
    return { data: null, error: null };
  }

  async getExecutionContext(
    executionId: string
  ): Promise<{ data: { context: Record<string, any> } | null; error: null }> {
    const execution = this.taskflowExecutions.get(executionId);
    if (!execution) {
      return { data: null, error: null };
    }
    return { data: { context: execution.context }, error: null };
  }

  async createTaskflow(
    schema: any,
    conversationId: string,
    active: boolean
  ): Promise<{ data: { id: string; schema: any; active: boolean }; error: null }> {
    const id = uuidv4();
    const taskflow: Taskflow = { id, schema, conversationId, active };
    this.taskflows.set(id, taskflow);
    return { data: { id, schema, active }, error: null };
  }

  async createSyncTrigger(params: {
    taskflowId: string;
    providerKey: string;
    model: string;
    syncKey: string;
    condition: any;
  }): Promise<{ data: SyncTrigger; error: null }> {
    const id = uuidv4();
    const createdAt = new Date();
    const syncTrigger: SyncTrigger = { id, createdAt, ...params };
    this.syncTriggers.set(id, syncTrigger);
    return { data: syncTrigger, error: null };
  }

  /*
    Utility functions not on the actual facade.
  */
  async addUser(
    userId: string,
    firstName?: string | null,
    lastName?: string | null,
    preferences?: any | null
  ): Promise<void> {
    this.profiles.set(userId, {
      id: userId,
      firstName: firstName ?? null,
      lastName: lastName ?? null,
      preferences: preferences ?? null,
    });
  }

  async addConversation(conversationId: string, userId: string): Promise<void> {
    this.conversations.set(conversationId, {
      id: conversationId,
      userId,
    });
  }

  async addConnection(connectionId: string, providerKey: string, userId: string): Promise<void> {
    this.connections.set(connectionId, {
      id: connectionId,
      providerKey,
      userId,
    });
  }
}
