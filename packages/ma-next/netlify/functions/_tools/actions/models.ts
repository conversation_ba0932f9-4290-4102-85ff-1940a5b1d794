// ---------------------------
// This file was generated by <PERSON><PERSON> (v0.59.0)
// It's recommended to version this file
// https://nango.dev
// ---------------------------

// ------ Models
export interface Attachments {
  filename: string;
  mimeType: string;
  size: number;
  attachmentId: string;
}

export interface GmailEmail {
  id: string;
  sender: string;
  recipients: string;
  date: string;
  subject: string;
  body: string;
  attachments: Attachments[];
  threadId: string;
  isDraft: boolean;
  labels: string[];
  snippet: string;
  cc: string;
  bcc: string;
  messageId: string;
  inReplyTo: string;
  references: string;
}

export interface GmailEmailLabelTriggered {
  id: string;
  sender: string;
  recipients: string;
  date: string;
  subject: string;
  body: string;
  attachments: Attachments[];
  threadId: string;
  isDraft: boolean;
  labels: string[];
  snippet: string;
  cc: string;
  bcc: string;
  messageId: string;
  inReplyTo: string;
  references: string;
}

export interface OptionalBackfillSetting {
  backfillPeriodMs?: number;
}

export interface TwitterPostInput {
  text: string;
  reply_to?: string;
  quote?: string;
}

export interface XSocialPostOutput {
  id: string;
  text: string;
  created_at: string;
}

export interface TwitterUserInput {
  username: string;
}

export interface ReferencedTweet {
  type: string;
  id: string;
}

export interface TweetMetrics {
  retweet_count: number;
  reply_count: number;
  like_count: number;
  quote_count: number;
}

export interface TwitterPost {
  id: string;
  text: string;
  created_at: string;
  author_id: string;
  conversation_id: string;
  in_reply_to_user_id?: string;
  referenced_tweets?: ReferencedTweet[];
  public_metrics: TweetMetrics;
}

export interface Mention {
  username: string;
  id: string;
}

export interface TwitterMention {
  id: string;
  text: string;
  created_at: string;
  author_id: string;
  conversation_id: string;
  in_reply_to_user_id?: string;
  referenced_tweets?: ReferencedTweet[];
  public_metrics: TweetMetrics;
  mention: Mention;
}

export interface XSocialUserProfile {
  id: string;
  name: string;
  username: string;
  profile_image_url: string;
  description: string;
  location: string;
  url: string;
  protected: boolean;
  verified: boolean;
  followers_count: number;
  following_count: number;
  tweet_count: number;
  listed_count: number;
}

export interface DropboxListFilesInput {
  path: string;
  recursive?: boolean;
  limit?: number;
  include_deleted?: boolean;
}

export interface DropboxEntry {
  '.tag': string;
  id: string;
  name: string;
  path_display: string;
  path_lower: string;
  client_modified?: string;
  server_modified?: string;
  rev?: string;
  size?: number;
  is_downloadable?: boolean;
  content_hash?: string;
}

export interface DropboxFileList {
  entries: DropboxEntry[];
  cursor?: string;
  has_more: boolean;
}

export interface DropboxGetFileInput {
  path: string;
}

export interface DropboxFile {
  id: string;
  name: string;
  path_display: string;
  path_lower: string;
  size: number;
  content_hash?: string;
  server_modified: string;
  content?: string;
  content_type?: string;
  download_url?: string;
}

export interface DropboxUploadFileInput {
  path: string;
  content: string;
  mode?: string;
  autorename?: boolean;
  mute?: boolean;
}

export interface DropboxCreateFolderInput {
  path: string;
  autorename?: boolean;
}

export interface DropboxFolder {
  id: string;
  name: string;
  path_display: string;
  path_lower: string;
}

export interface DropboxDeleteInput {
  path: string;
}

export interface DropboxDeleteResult {
  metadata: DropboxEntry;
}

export interface DropboxMoveInput {
  from_path: string;
  to_path: string;
  allow_shared_folder?: boolean;
  autorename?: boolean;
  allow_ownership_transfer?: boolean;
}

export interface DropboxCopyInput {
  from_path: string;
  to_path: string;
  allow_shared_folder?: boolean;
  autorename?: boolean;
}

export interface DropboxSearchInput {
  query: string;
  path?: string;
  max_results?: number;
  mode?: string;
}

export interface DropboxSearchMatch {
  metadata: DropboxEntry;
  match_type: string;
}

export interface DropboxSearchResult {
  matches: DropboxSearchMatch[];
  more: boolean;
  start: number;
}

export interface GithubRepositoryInput {
  owner: string;
  repo: string;
}

export interface GithubIssueCreator {
  login: string;
  id: number;
  node_id: string;
  avatar_url: string;
  gravatar_id: string;
  url: string;
  html_url: string;
  followers_url: string;
  following_url: string;
  gists_url: string;
  starred_url: string;
  subscriptions_url: string;
  organizations_url: string;
  repos_url: string;
  events_url: string;
  received_events_url: string;
  type: string;
  user_view_type: string;
  site_admin: boolean;
}

export interface GithubRepository {
  id: number;
  node_id: string;
  name: string;
  full_name: string;
  private: boolean;
  owner: GithubIssueCreator;
  html_url: string;
  description: string | null;
  fork: boolean;
  url: string;
  forks_url: string;
  keys_url: string;
  collaborators_url: string;
  teams_url: string;
  hooks_url: string;
  issue_events_url: string;
  events_url: string;
  assignees_url: string;
  branches_url: string;
  tags_url: string;
  blobs_url: string;
  git_tags_url: string;
  git_refs_url: string;
  trees_url: string;
  statuses_url: string;
  languages_url: string;
  stargazers_url: string;
  contributors_url: string;
  subscribers_url: string;
  subscription_url: string;
  commits_url: string;
  git_commits_url: string;
  comments_url: string;
  issue_comment_url: string;
  contents_url: string;
  compare_url: string;
  merges_url: string;
  archive_url: string;
  downloads_url: string;
  issues_url: string;
  pulls_url: string;
  milestones_url: string;
  notifications_url: string;
  labels_url: string;
  releases_url: string;
  deployments_url: string;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  git_url: string;
  ssh_url: string;
  clone_url: string;
  svn_url: string;
  homepage: string | null;
  size: number;
  stargazers_count: number;
  watchers_count: number;
  language: string | null;
  has_issues: boolean;
  has_projects: boolean;
  has_downloads: boolean;
  has_wiki: boolean;
  has_pages: boolean;
  has_discussions: boolean;
  forks_count: number;
  mirror_url: string | null;
  archived: boolean;
  disabled: boolean;
  open_issues_count: number;
  license: string | null;
  allow_forking: boolean;
  is_template: boolean;
  web_commit_signoff_required: boolean;
  topics: string[];
  visibility: string;
  forks: number;
  open_issues: number;
  watchers: number;
  default_branch: string;
}

export interface GithubRepositoryList {
  note: string | null;
  repositories: GithubRepository[];
}

export interface GithubCreateRepositoryInput {
  name: string;
  description?: string;
  private?: boolean;
  has_issues?: boolean;
  has_projects?: boolean;
  has_wiki?: boolean;
  auto_init?: boolean;
  gitignore_template?: string;
  license_template?: string;
}

export interface GithubUpdateRepositoryInput {
  owner: string;
  repo: string;
  name?: string;
  description?: string;
  private?: boolean;
  has_issues?: boolean;
  has_projects?: boolean;
  has_wiki?: boolean;
  default_branch?: string;
}

export interface GithubDeleteRepositoryOutput {
  success: boolean;
  message: string;
}

export interface GithubCreateOrganizationRepositoryInput {
  org: string;
  name: string;
  description?: string;
  homepage?: string;
  private?: boolean;
  has_issues?: boolean;
  has_projects?: boolean;
  has_wiki?: boolean;
}

export interface GithubIssuesInput {
  owner: string;
  repo: string;
  state?: string;
  sort?: string;
  direction?: string;
  per_page?: number;
  page?: number;
}

export interface GithubIssueInput {
  owner: string;
  repo: string;
  issue_number: number;
}

export interface GithubIssueCreatorLite {
  login: string;
  id: number;
  avatar_url: string;
  html_url: string;
}

export interface GithubIssueLabel {
  id: number;
  name: string;
  color: string;
  description: string;
}

export interface GithubSubIssuesSummary {
  total: number;
  completed: number;
  percent_completed: number;
}

export interface GithubReactions {
  url: string;
  total_count: number;
  '+1': number;
  '-1': number;
  laugh: number;
  hooray: number;
  confused: number;
  heart: number;
  rocket: number;
  eyes: number;
}

export interface GithubIssue {
  id: number;
  node_id: string;
  url: string;
  repository_url: string;
  labels_url: string;
  comments_url: string;
  events_url: string;
  html_url: string;
  number: number;
  title: string;
  state: string;
  locked: boolean;
  body: string | null;
  user: GithubIssueCreatorLite | GithubIssueCreator;
  labels: GithubIssueLabel[];
  assignee: Record<string, any> | null;
  assignees: Record<string, any>[];
  milestone: string | null;
  comments: number;
  created_at: string;
  updated_at: string;
  closed_at: string | null;
  author_association: string;
  active_lock_reason: string | null;
  sub_issues_summary: GithubSubIssuesSummary;
  closed_by: GithubIssueCreator | null;
  reactions: GithubReactions;
  timeline_url: string;
  performed_via_github_app: Record<string, any> | null;
  state_reason: string | null;
}

export interface GithubIssueAssignee {
  id: number;
  login: string;
  avatar_url: string;
  html_url: string;
}

export interface GithubIssueList {
  issues: GithubIssue[];
}

export interface GithubCreateIssueInput {
  owner: string;
  repo: string;
  title: string;
  body?: string;
  assignees?: string[];
  labels?: string[];
}

export interface GithubUpdateIssueInput {
  owner: string;
  repo: string;
  issue_number: number;
  title?: string;
  body?: string;
  state?: string;
  assignees?: string[];
  labels?: string[];
}

export interface GithubPullRequestInput {
  owner: string;
  repo: string;
  pullNumber: number;
}

export interface GithubTeamRef {
  id: number;
  name: string;
}

export interface GithubPullRequest {
  url: string;
  id: number;
  node_id: string;
  html_url: string;
  diff_url: string;
  patch_url: string;
  issue_url: string;
  number: number;
  state: string;
  locked: boolean;
  title: string;
  user: GithubIssueCreator;
  body: string | null;
  created_at: string;
  updated_at: string;
  closed_at: string | null;
  merged_at: string | null;
  merge_commit_sha: string | null;
  assignee: GithubIssueAssignee | null;
  assignees: GithubIssueAssignee[];
  requested_reviewers: GithubIssueAssignee[];
  requested_teams: GithubTeamRef[];
  labels: GithubIssueLabel[];
  milestone: string | null;
  draft: boolean;
  commits_url: string;
  review_comments_url: string;
  review_comment_url: string;
  comments_url: string;
  statuses_url: string;
  head: Record<string, any>;
  base: Record<string, any>;
  _links: Record<string, any>;
  author_association: string;
  auto_merge: Record<string, any> | null;
  active_lock_reason: string | null;
  merged: boolean;
  mergeable: boolean | null;
  rebaseable: boolean | null;
  mergeable_state: string;
  merged_by: GithubIssueCreator | null;
  comments: number;
  review_comments: number;
  maintainer_can_modify: boolean;
  commits: number;
  additions: number;
  deletions: number;
  changed_files: number;
}

export interface GithubPullRequestBranch {
  label: string;
  ref: string;
  sha: string;
  user: GithubIssueCreator;
}

export interface GithubTeam {
  id: number;
  name: string;
  slug: string;
  description: string;
  privacy: string;
  url: string;
  html_url: string;
  members_url: string;
  repositories_url: string;
  permission: string;
}

export interface GithubUpdatePullRequestInput {
  owner: string;
  repo: string;
  pullNumber: number;
  title?: string;
  body?: string;
  state?: string;
  base?: string;
  maintainer_can_modify?: boolean;
}

export interface GithubListPullRequestsInput {
  owner: string;
  repo: string;
  state?: string;
  head?: string;
  base?: string;
  sort?: string;
  direction?: string;
  per_page?: number;
  page?: number;
}

export interface GithubPullRequestList {
  pull_requests: GithubPullRequest[];
}

export interface GithubMergePullRequestInput {
  owner: string;
  repo: string;
  pullNumber: number;
  commit_title?: string;
  commit_message?: string;
  merge_method?: string;
}

export interface GithubMergeResult {
  sha: string;
  merged: boolean;
  message: string;
}

export interface GithubPullRequestFile {
  sha: string;
  filename: string;
  status: string;
  additions: number;
  deletions: number;
  changes: number;
  blob_url: string;
  raw_url: string;
  contents_url: string;
  patch: string;
}

export interface GithubPullRequestFileList {
  files: GithubPullRequestFile[];
}

export interface GithubStatus {
  url: string;
  id: number;
  node_id: string;
  state: string;
  context: string;
  description: string;
  target_url: string;
  created_at: string;
  updated_at: string;
}

export interface GithubRepositoryForGithubCombinedStatus {
  id: number;
  node_id: string;
  name: string;
  full_name: string;
  private: boolean;
  owner: GithubIssueCreator;
  html_url: string;
  description: string;
  fork: boolean;
  url: string;
  forks_url: string;
  keys_url: string;
  collaborators_url: string;
  teams_url: string;
  hooks_url: string;
  issue_events_url: string;
  events_url: string;
  assignees_url: string;
  branches_url: string;
  tags_url: string;
  blobs_url: string;
  git_tags_url: string;
  git_refs_url: string;
  trees_url: string;
  statuses_url: string;
  languages_url: string;
  stargazers_url: string;
  contributors_url: string;
  subscribers_url: string;
  subscription_url: string;
  commits_url: string;
  git_commits_url: string;
  comments_url: string;
  issue_comment_url: string;
  contents_url: string;
  compare_url: string;
  merges_url: string;
  archive_url: string;
  downloads_url: string;
  issues_url: string;
  pulls_url: string;
  milestones_url: string;
  notifications_url: string;
  labels_url: string;
  releases_url: string;
  deployments_url: string;
}

export interface GithubCombinedStatus {
  state: string;
  sha: string;
  total_count: number;
  statuses: GithubStatus[];
  repository: GithubRepositoryForGithubCombinedStatus;
  commit_url: string;
}

export interface GithubUpdatePullRequestBranchInput {
  owner: string;
  repo: string;
  pullNumber: number;
  expectedHeadSha?: string;
}

export interface GithubBranchUpdateResult {
  message: string;
  url: string;
}

export interface GithubPullRequestComment {
  id: number;
  node_id: string;
  url: string;
  pull_request_review_id: number;
  diff_hunk: string;
  path: string;
  position: number;
  original_position: number;
  commit_id: string;
  original_commit_id: string;
  user: GithubIssueCreator;
  body: string;
  created_at: string;
  updated_at: string;
  html_url: string;
  pull_request_url: string;
  author_association: string;
  _links: Record<string, any>;
  reactions: GithubReactions;
  start_line: number | null;
  original_start_line: number | null;
  start_side: string | null;
  line: number;
  original_line: number;
  side: string;
  in_reply_to_id?: number;
  subject_type: string;
}

export interface GithubPullRequestCommentList {
  comments: GithubPullRequestComment[];
}

export interface GithubAddPullRequestReviewCommentInput {
  owner: string;
  repo: string;
  pull_number: number;
  body: string;
  commit_id?: string;
  path?: string;
  subject_type?: string;
  line?: number;
  side?: string;
  start_line?: number;
  start_side?: string;
  in_reply_to?: number;
  diff_hunk?: string;
}

export interface GithubPullRequestReview {
  id: number;
  node_id: string;
  user: GithubIssueCreator;
  body: string;
  state: string;
  html_url: string;
  pull_request_url: string;
  submitted_at: string;
  commit_id: string;
  author_association: string;
  _links: Record<string, any>;
}

export interface GithubDraftReviewComment {
  path: string;
  position?: number;
  line?: number;
  side?: string;
  start_line?: number;
  start_side?: string;
  body: string;
}

export interface GithubCreatePullRequestReviewInput {
  owner: string;
  repo: string;
  pullNumber: number;
  body?: string;
  event: string;
  commitId?: string;
  comments?: GithubDraftReviewComment[];
}

export interface GithubCreatePullRequestInput {
  owner: string;
  repo: string;
  title: string;
  body?: string;
  head: string;
  base: string;
  draft?: boolean;
  maintainer_can_modify?: boolean;
}

export interface GoogleCalendarList {
  calendars: {
    id: string;
    summary: string;
    description?: string;
    location?: string;
    timeZone: string;
    accessRole: string;
    primary: boolean;
    backgroundColor?: string;
    foregroundColor?: string;
  }[];
  nextPageToken?: string;
}

export interface GoogleCalendarEventsInput {
  calendarId: string;
  timeMin?: string;
  timeMax?: string;
  maxResults?: number;
  pageToken?: string;
  orderBy?: string;
  q?: string;
  singleEvents?: boolean;
  timeZone?: string;
}

export interface GoogleCalendarEventList {
  events: {
    id: string;
    summary: string;
    description?: string;
    location?: string;
    start: { dateTime?: string; date?: string; timeZone?: string };
    end: { dateTime?: string; date?: string; timeZone?: string };
    status: string;
    creator: { id?: string; email: string; displayName?: string; self?: boolean };
    organizer: { id?: string; email: string; displayName?: string; self?: boolean };
    attendees?: {
      id?: string;
      email: string;
      displayName?: string;
      responseStatus: string;
      optional?: boolean;
      resource?: boolean;
      comment?: string;
    }[];
    htmlLink: string;
    created: string;
    updated: string;
    recurrence?: string[];
  }[];
  nextPageToken?: string;
  timeZone: string;
}

export interface GoogleCalendarEventInput {
  summary: string;
  description?: string;
  location?: string;
  start: string;
  end: string;
  timeZone?: string;
  attendees?: string[];
}

export interface GoogleCalendarEventDateTimeInput {
  dateTime?: string;
  date?: string;
  timeZone?: string;
}

export interface GoogleCalendarAttendeeInput {
  id?: string;
  email: string;
  displayName?: string;
  responseStatus: string;
  optional?: boolean;
  resource?: boolean;
  comment?: string;
}

export interface GoogleCalendarEventUpdateInput {
  calendarId: string;
  eventId: string;
  sendUpdates?: string;
  summary?: string;
  description?: string;
  location?: string;
  start?: string;
  end?: string;
  timeZone?: string;
  attendees?: GoogleCalendarAttendeeInput[];
}

export interface GoogleCalendarEventOutput {
  kind: string;
  etag: string;
  id: string;
  status: string;
  htmlLink: string;
  created: string;
  updated: string;
  summary: string;
  description?: string;
  location?: string;
  creator: { email: string; self?: boolean };
  organizer: { email: string; self?: boolean };
  start: { dateTime?: string; date?: string; timeZone?: string };
  end: { dateTime?: string; date?: string; timeZone?: string };
  iCalUID: string;
  sequence: number;
  reminders: { useDefault: boolean };
  eventType: string;
  attendees?: {
    email: string;
    responseStatus?: string;
    displayName?: string;
    optional?: boolean;
    resource?: boolean;
    comment?: string;
  }[];
  recurrence?: string[];
  anyoneCanAddSelf?: boolean;
  guestsCanInviteOthers?: boolean;
  guestsCanSeeOtherGuests?: boolean;
  guestsCanModify?: boolean;
  privateCopy?: boolean;
  transparency?: string;
  visibility?: string;
  colorId?: string;
  attachments?: { fileUrl: string; title: string; mimeType: string; fileId: string }[];
}

export interface GoogleCalendarEventDeleteInput {
  calendarId: string;
  eventId: string;
  sendUpdates?: string;
}

export interface GoogleDocsGetDocumentInput {
  documentId: string;
}

export interface GoogleDocsCreateDocumentInput {
  title?: string;
}

export interface GoogleDocsUpdateDocumentInput {
  documentId: string;
  requests: Record<string, any>[];
  writeControl?: Record<string, any>;
}

export interface GoogleDocsUpdateDocumentOutput {
  documentId: string;
  replies?: Record<string, any>[];
  writeControl?: Record<string, any>;
}

export interface GoogleDocsDocument {
  documentId?: string;
  title?: string;
  body?: Record<string, any>;
  headers?: Record<string, any>;
  footers?: Record<string, any>;
  footnotes?: Record<string, any>;
  documentStyle?: Record<string, any>;
  namedStyles?: Record<string, any>;
  revisionId?: string;
  suggestionsViewMode?: string;
  inlineObjects?: Record<string, any>;
  positionedObjects?: Record<string, any>;
  tabs?: Record<string, any>[];
}

export interface GoogleDriveDocument {
  id: string;
  name: string;
  mimeType: string;
  webViewLink: string;
  modifiedTime: string;
  createdTime: string;
  parents: string[];
  size: string;
}

export interface GoogleDriveFolder {
  id: string;
  name: string;
  mimeType: string;
  webViewLink: string;
  modifiedTime: string;
  createdTime: string;
  parents: string[];
}

export interface ListDocumentsInput {
  folderId?: string;
  mimeType?: string;
  pageSize?: number;
  pageToken?: string;
  orderBy?: string;
}

export interface GoogleDriveDocumentList {
  documents: GoogleDriveDocument[];
  nextPageToken?: string;
}

export interface GoogleDriveFolderList {
  folders: GoogleDriveFolder[];
  nextPageToken?: string;
}

export interface GmailDraftInput {
  recipient: string;
  subject: string;
  body?: string;
  headers?: Record<string, any>;
}

export interface GmailDraftOutput {
  id: string;
  threadId: string | null;
}

export interface GmailReplyDraftInput {
  sender: string;
  subject: string;
  body: string;
  threadId: string;
  messageId: string;
  inReplyTo: string;
  references: string;
  date: string;
  replyBody: string;
}

export interface GmailReplyDraftOutput {
  id: string;
  threadId: string | null;
}

export interface GmailListMessagesInput {
  maxResults?: number;
  labelIds?: string[];
  q?: string;
  pageToken?: string;
}

export interface GmailBasicMessageDetails {
  id: string;
  threadId: string;
  labelIds?: string[];
  snippet?: string;
  subject?: string;
  date?: string;
}

export interface GmailMessageList {
  messages: GmailBasicMessageDetails[];
  nextPageToken?: string;
}

export interface GmailGetMessageInput {
  id: string;
  format?: string;
}

export interface GmailHeader {
  name: string;
  value: string;
}

export interface GmailAttachmentInfo {
  filename: string;
  mimeType: string;
  size: number;
  attachmentId?: string;
}

export interface GmailMessage {
  id: string;
  threadId: string;
  labelIds?: string[];
  snippet?: string;
  payload?: Record<string, any>;
  sizeEstimate?: number;
  historyId?: string;
  internalDate?: string;
  headers?: GmailHeader[];
  body?: string;
  mimeType?: string;
  filename?: string;
  attachments?: GmailAttachmentInfo[];
}

export interface GmailMessageIdInput {
  messageId: string;
}

export interface GmailModifyMessageLabelsInput {
  messageId: string;
  addLabelIds?: string[];
  removeLabelIds?: string[];
}

export interface GmailDeleteMessageOutput {
  success: boolean;
}

export interface GmailSearchMessagesInput {
  q: string;
  maxResults?: number;
  labelIds?: string[];
  pageToken?: string;
}

export interface GmailAttachment {
  filename: string;
  content: string;
  mimeType: string;
}

export interface GmailSendEmailInput {
  to: string;
  subject: string;
  body: string;
  from?: string;
  cc?: string;
  bcc?: string;
  attachments?: GmailAttachment[];
}

export interface GmailSendEmailOutput {
  id: string;
  threadId: string;
  labelIds?: string[];
}

export interface SheetRow {
  cells: string[];
}

export interface SheetData {
  rows: SheetRow[];
}

export interface GoogleSheetTab {
  title: string;
  data?: SheetData;
}

export interface GoogleSheetCreateInput {
  title: string;
  sheets?: GoogleSheetTab[];
}

export interface GoogleSheetCreateOutput {
  id: string;
  url: string;
  title: string;
}

export interface SheetUpdate {
  sheetId?: number;
  sheetName?: string;
  range?: string;
  startRow?: number;
  startColumn?: number;
  data: SheetData;
}

export interface GoogleSheetUpdateInput {
  spreadsheetId: string;
  updates: SheetUpdate[];
}

export interface GoogleSheetUpdateOutput {
  spreadsheetId: string;
  updatedRange: string;
  updatedRows: number;
  updatedColumns: number;
  updatedCells: number;
}

export interface HarvestCreateProjectInput {
  client_id: number;
  name: string;
  is_billable: boolean;
  bill_by: string;
  budget_by: string;
  is_fixed_fee?: boolean;
  fee?: number | null;
  hourly_rate?: number | null;
  budget?: number | null;
  budget_is_monthly?: boolean;
  notify_when_over_budget?: boolean;
  over_budget_notification_percentage?: number;
  show_budget_to_all?: boolean;
  cost_budget?: number | null;
  cost_budget_include_expenses?: boolean;
  notes?: string | null;
  starts_on?: string | null;
  ends_on?: string | null;
}

export interface HarvestCreateClientInput {
  name: string;
  is_active?: boolean;
  address?: string | null;
  currency?: string | null;
}

export interface HarvestTasksInput {
  is_active?: boolean;
  updated_since?: string;
  page?: number;
  per_page?: number;
}

export interface HarvestTask {
  id: number;
  name: string;
  is_active: boolean;
  billable_by_default: boolean;
  is_default?: boolean;
  created_at: string;
  updated_at: string;
  default_hourly_rate?: number | null;
}

export interface HarvestTaskList {
  tasks: HarvestTask[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page: number;
  links?: Record<string, any>;
}

export interface HarvestTaskInTimeEntry {
  id: number;
  name: string;
  is_active?: boolean;
  billable_by_default?: boolean;
  is_default?: boolean;
  created_at?: string;
  updated_at?: string;
  default_hourly_rate?: number | null;
}

export interface HarvestTimeEntriesInput {
  userId?: number;
  clientId?: number;
  projectId?: number;
  taskId?: number;
  from?: string;
  to?: string;
  page?: number;
  perPage?: number;
}

export interface HarvestTimeEntryInput {
  timeEntryId: number;
  id?: number;
}

export interface HarvestUser {
  id: number;
  name: string;
  email?: string;
}

export interface HarvestClientReference {
  id: number;
  name: string;
  currency: string;
}

export interface HarvestClient {
  id: number;
  name: string;
  is_active: boolean;
  address?: string | null;
  statement_key?: string;
  created_at?: string;
  updated_at?: string;
  currency?: string;
}

export interface HarvestClientInTimeEntry {
  id: number;
  name: string;
  currency?: string;
  is_active?: boolean;
}

export interface HarvestProject {
  id: number;
  name: string;
  code?: string | null;
  client: HarvestClientReference;
  is_active: boolean;
  is_billable: boolean;
  is_fixed_fee: boolean;
  bill_by: string;
  budget?: number | null;
  budget_by: string;
  budget_is_monthly: boolean;
  notify_when_over_budget: boolean;
  over_budget_notification_percentage?: number;
  show_budget_to_all: boolean;
  created_at: string;
  updated_at: string;
  starts_on?: string | null;
  ends_on?: string | null;
  over_budget_notification_date?: string | null;
  notes?: string | null;
  cost_budget?: number | null;
  cost_budget_include_expenses: boolean;
  hourly_rate?: number | null;
  fee?: number | null;
}

export interface ProjectReference {
  id: number;
  name: string;
  code?: string | null;
  is_active?: boolean;
  is_billable?: boolean;
}

export interface HarvestExternalReferenceInput {
  id?: string;
  group_id?: string;
  account_id?: string;
  permalink?: string;
  service?: string;
  service_icon_url?: string;
}

export interface HarvestTimeEntry {
  id: number;
  spent_date: string;
  hours: number;
  notes?: string;
  is_locked: boolean;
  is_running: boolean;
  is_billed: boolean;
  timer_started_at?: string | null;
  started_time?: string | null;
  ended_time?: string | null;
  user: HarvestUser;
  client: HarvestClientInTimeEntry;
  project: ProjectReference;
  task: HarvestTaskInTimeEntry;
  created_at: string;
  updated_at: string;
  hours_without_timer?: number;
  rounded_hours?: number;
  locked_reason?: string | null;
  is_closed?: boolean;
  billable?: boolean;
  budgeted?: boolean;
  billable_rate?: number | null;
  cost_rate?: number | null;
  user_assignment?: Record<string, any>;
  task_assignment?: Record<string, any>;
  invoice?: Record<string, any> | null;
  external_reference?: HarvestExternalReferenceInput | null;
}

export interface HarvestTimeEntryList {
  time_entries: HarvestTimeEntry[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: Record<string, any>;
}

export interface HarvestCompany {
  id: number;
  name: string;
  is_active: boolean;
  base_uri: string;
  full_domain: string;
  currency: string;
  thousands_separator: string;
  decimal_separator: string;
  timezone: string;
  week_start_day: string;
  time_format: string;
  date_format: string;
  users_can_create_projects: boolean;
  users_can_create_invoices: boolean;
  expense_feature_enabled: boolean;
  invoice_feature_enabled: boolean;
  wants_timestamp_timers: boolean;
  created_at: string;
  updated_at: string;
}

export interface HarvestAddHistoricalTimeEntryInput {
  project_id: number;
  task_id: number;
  spent_date: string;
  hours?: number;
  started_time?: string;
  ended_time?: string;
  notes?: string;
  user_id?: number;
  external_reference?: HarvestExternalReferenceInput;
}

export interface HarvestStartTimerInput {
  project_id: number;
  task_id: number;
  spent_date: string;
  started_time?: string;
  notes?: string;
  user_id?: number;
  external_reference?: HarvestExternalReferenceInput;
}

export interface HarvestUpdateTimeEntryInput {
  time_entry_id: number;
  project_id?: number;
  task_id?: number;
  spent_date?: string;
  hours?: number;
  started_time?: string;
  ended_time?: string;
  notes?: string;
  external_reference?: HarvestExternalReferenceInput;
}

export interface HarvestDeleteTimeEntryOutput {
  success: boolean;
  message: string;
}

export interface HarvestProjectsInput {
  client_id?: number;
  is_active?: boolean;
  page?: number;
  per_page?: number;
}

export interface HarvestProjectInput {
  project_id: number;
}

export interface HarvestPaginationLinks {
  first?: string;
  next?: string | null;
  previous?: string | null;
  last?: string;
}

export interface HarvestProjectList {
  projects: HarvestProject[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: HarvestPaginationLinks;
}

export interface HarvestClientInput {
  client_id: number;
}

export interface HarvestClientList {
  clients: HarvestClient[];
  per_page: number;
  total_pages: number;
  total_entries: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: Record<string, any>;
}

export interface HarvestProjectTasksInput {
  project_id: number;
}

export interface TaskInAssignment {
  id: number;
  name: string;
}

export interface PaginationLinks {
  first?: string;
  next?: string | null;
  previous?: string | null;
  last?: string;
}

export interface HarvestProjectTask {
  id: number;
  billable: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  hourly_rate?: number | null;
  budget?: number | null;
  project?: ProjectReference;
  task: TaskInAssignment;
}

export interface HarvestProjectTaskList {
  task_assignments: HarvestProjectTask[];
  per_page?: number;
  total_pages?: number;
  total_entries?: number;
  next_page?: number | null;
  previous_page?: number | null;
  page?: number;
  links?: PaginationLinks;
}

export interface HarvestDeleteProjectOutput {
  success: boolean;
  message: string;
}

export interface LinearIssuesInput {
  teamId?: string;
  projectId?: string;
  states?: string[];
  assigneeId?: string;
  priority?: number;
  sortBy?: string;
  sortOrder?: string;
  limit?: number;
  first?: number;
  after?: string;
}

export interface LinearIssueInput {
  issueId: string;
}

export interface LinearUser {
  id: string;
  name: string;
  email: string;
  displayName?: string;
  avatarUrl?: string | null;
}

export interface LinearState {
  id: string;
  name: string;
  color: string;
  type: string;
}

export interface LinearTeamBasic {
  id: string;
  name: string;
  key: string;
}

export interface LinearProjectBasic {
  id: string;
  name: string;
  icon?: string;
  color?: string;
}

export interface LinearLabel {
  id: string;
  name: string;
  color: string;
}

export interface LinearIssue {
  id: string;
  title: string;
  description?: string;
  number: number;
  priority: number;
  url: string;
  createdAt: string;
  updatedAt: string;
  state: LinearState;
  assignee?: LinearUser;
  team: LinearTeamBasic;
  project?: LinearProjectBasic;
  labels?: LinearLabel[];
}

export interface PageInfo {
  hasNextPage: boolean;
  endCursor?: string;
}

export interface LinearIssueList {
  issues: LinearIssue[];
  pageInfo?: PageInfo;
}

export interface LinearCreateIssueInput {
  teamId: string;
  title: string;
  description?: string;
  stateId?: string;
  assigneeId?: string;
  priority?: number;
  projectId?: string;
  labelIds?: string[];
}

export interface LinearUpdateIssueInput {
  issueId: string;
  title?: string;
  description?: string;
  stateId?: string;
  assigneeId?: string;
  priority?: number;
  projectId?: string;
  labelIds?: string[];
}

export interface LinearDeleteIssueOutput {
  success: boolean;
  issueId: string;
}

export interface LinearTeamInput {
  teamId: string;
}

export interface LinearTeam {
  id: string;
  name: string;
  key: string;
  description?: string | null;
  color?: string;
  private?: boolean;
  createdAt: string;
  updatedAt: string;
  members: LinearUser[];
}

export interface LinearTeamList {
  teams: LinearTeam[];
  pageInfo?: PageInfo;
}

export interface LinearTeamsInput {
  first?: number;
  after?: string;
}

export interface LinearProjectsInput {
  first?: number;
  after?: string;
}

export interface LinearProjectInput {
  projectId: string;
}

export interface LinearUserBasic {
  id: string;
  name: string;
  email?: string;
}

export interface LinearProject {
  id: string;
  name: string;
  description?: string;
  url?: string;
  color?: string;
  state: string;
  lead?: LinearUserBasic;
  teams: LinearTeamBasic[];
  createdAt: string;
  updatedAt: string;
}

export interface LinearProjectList {
  projects: LinearProject[];
  pageInfo?: PageInfo;
}

export interface LinearCreateProjectInput {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  teamIds: string[];
}

export interface LinearUpdateProjectInput {
  projectId: string;
  name?: string;
  description?: string;
  icon?: string;
  color?: string;
  state?: string;
  teamIds?: string[];
}

export interface LinkedInUserProfile {
  sub: string;
  email_verified: boolean;
  name: string;
  locale: Record<string, any>;
  given_name: string;
  family_name: string;
  email: string;
  picture: string;
}

export interface LinkedInPostInput {
  text: string;
  visibility: string;
}

export interface LinkedInPostOutput {
  id: string;
}

export interface NotionSort {
  direction?: string;
  timestamp?: string;
}

export interface NotionFilter {
  value?: string;
  property?: string;
}

export interface NotionSearchInput {
  query?: string;
  sort?: NotionSort;
  filter?: NotionFilter;
  start_cursor?: string;
  page_size?: number;
}

export interface NotionGetPageInput {
  pageId: string;
}

export interface NotionGetDatabaseInput {
  databaseId: string;
}

export interface NotionRichTextContent {
  content?: string;
  link?: Record<string, any> | null;
}

export interface NotionRichTextAnnotations {
  bold?: boolean;
  italic?: boolean;
  strikethrough?: boolean;
  underline?: boolean;
  code?: boolean;
  color?: string;
}

export interface NotionRichText {
  type?: string;
  text?: NotionRichTextContent;
  annotations?: NotionRichTextAnnotations;
  plain_text?: string;
  href?: string | null;
}

export interface NotionCreateDatabaseInput {
  parentId: string;
  title: NotionRichText[];
  properties: Record<string, any>;
}

export interface NotionQueryDatabaseInput {
  databaseId: string;
  filter?: Record<string, any>;
  sorts?: Record<string, any>[];
  start_cursor?: string;
  page_size?: number;
}

export interface NotionCreatePageInput {
  parentId: string;
  parentType?: string;
  properties: Record<string, any>;
  children?: Record<string, any>[];
}

export interface NotionUpdatePageInput {
  pageId: string;
  properties?: Record<string, any>;
  archived?: boolean;
  icon?: Record<string, any> | null;
  cover?: Record<string, any> | null;
}

export interface NotionUpdateDatabaseInput {
  databaseId: string;
  title?: NotionRichText[];
  description?: NotionRichText[];
  properties?: Record<string, any>;
  archived?: boolean;
}

export interface NotionUserReference {
  object: string;
  id: string;
}

export interface NotionParentReference {
  type: string;
  workspace?: boolean;
  page_id?: string;
  database_id?: string;
}

export interface NotionCover {
  _placeholder?: string;
}

export interface NotionIcon {
  _placeholder?: string;
}

export interface NotionTitleProperty {
  id?: string;
  type?: string;
  title?: NotionRichText[];
}

export interface NotionProperties {
  title?: NotionTitleProperty;
}

export interface NotionGenericObjectPlaceholder {
  _placeholder?: string;
}

export interface NotionPageOrDatabase {
  object: string;
  id: string;
  created_time?: string;
  last_edited_time?: string;
  created_by?: NotionUserReference;
  last_edited_by?: NotionUserReference;
  cover?: NotionCover | null;
  icon?: NotionIcon | null;
  parent?: NotionParentReference;
  archived?: boolean;
  in_trash?: boolean;
  properties?: NotionProperties;
  url?: string;
  public_url?: string | null;
  title?: NotionRichText[];
  description?: NotionRichText[];
  is_inline?: boolean;
  request_id?: string;
}

export interface NotionDatabase {
  object: string;
  id: string;
  created_time?: string;
  last_edited_time?: string;
  created_by?: NotionUserReference;
  last_edited_by?: NotionUserReference;
  icon?: NotionIcon | null;
  cover?: NotionCover | null;
  parent?: NotionParentReference;
  archived?: boolean;
  in_trash?: boolean;
  properties: NotionProperties;
  url?: string;
  public_url?: string | null;
  title?: NotionRichText[];
  description?: NotionRichText[];
  is_inline?: boolean;
  request_id?: string;
}

export interface NotionQueryDatabaseOutput {
  object: string;
  results: NotionPageOrDatabase[];
  next_cursor?: string | null;
  has_more: boolean;
  type?: string;
  page?: NotionGenericObjectPlaceholder;
  request_id?: string;
}

export interface NotionSearchOutput {
  object: string;
  results: NotionPageOrDatabase[];
  next_cursor?: string | null;
  has_more: boolean;
  type?: string;
  page_or_database?: NotionGenericObjectPlaceholder;
  request_id?: string;
}

export interface SlackSendMessageInput {
  channel: string;
  text: string;
  thread_ts?: string;
}

export interface SlackSendMessageOutput {
  ok: boolean;
  ts: string;
  channel: string;
  message_text?: string;
}

export interface SlackListChannelsInput {
  types?: string;
  limit?: number;
  cursor?: string;
}

export interface SlackChannel {
  id: string;
  name: string;
  is_private: boolean;
  is_member: boolean;
  num_members?: number;
}

export interface SlackChannelList {
  channels: SlackChannel[];
  next_cursor?: string;
}

export interface SlackGetChannelHistoryInput {
  channel: string;
  limit?: number;
  latest?: string;
  oldest?: string;
  cursor?: string;
}

export interface SlackBlock {
  type: string;
  block_id?: string;
  text?: Record<string, any>;
  elements?: Record<string, any>[];
  fields?: Record<string, any>[];
  accessory?: Record<string, any>;
}

export interface SlackAttachment {
  id?: number;
  fallback?: string;
  color?: string;
  pretext?: string;
  author_name?: string;
  author_link?: string;
  author_icon?: string;
  title?: string;
  title_link?: string;
  text?: string;
  fields?: Record<string, any>[];
  image_url?: string;
  thumb_url?: string;
  footer?: string;
  footer_icon?: string;
  ts?: number;
}

export interface SlackFile {
  id: string;
  name?: string;
  filetype?: string;
  url_private?: string;
  url_private_download?: string;
  mimetype?: string;
  size?: number;
  title?: string;
  created?: number;
  timestamp?: number;
  user?: string;
  editable?: boolean;
  mode?: string;
  is_external?: boolean;
  external_type?: string;
  permalink?: string;
  preview?: string;
}

export interface SlackReaction {
  name: string;
  count: number;
  users: string[];
}

export interface SlackEdited {
  user: string;
  ts: string;
}

export interface SlackMessage {
  type: string;
  subtype?: string;
  ts: string;
  user?: string;
  text: string;
  thread_ts?: string;
  reply_count?: number;
  blocks?: SlackBlock[];
  attachments?: SlackAttachment[];
  files?: SlackFile[];
  reactions?: SlackReaction[];
  parent_user_id?: string;
  edited?: SlackEdited;
  bot_id?: string;
  icons?: Record<string, any>;
  team?: string;
  app_id?: string;
  client_msg_id?: string;
}

export interface SlackResponseMetadata {
  next_cursor?: string;
}

export interface SlackMessageList {
  ok: boolean;
  messages: SlackMessage[];
  has_more?: boolean;
  pin_count?: number;
  channel_actions_ts?: string;
  channel_actions_count?: number;
  response_metadata?: SlackResponseMetadata;
  error?: string;
}

export interface SlackGetUserInfoInput {
  user: string;
}

export interface SlackUserProfile {
  real_name?: string;
  display_name?: string;
  email?: string;
  image_original?: string;
  image_512?: string;
}

export interface SlackUserInfo {
  id: string;
  name: string;
  is_bot: boolean;
  is_admin?: boolean;
  is_owner?: boolean;
  tz?: string;
  profile?: SlackUserProfile;
}

export interface SlackAddReactionInput {
  name: string;
  channel: string;
  timestamp: string;
}

export interface SlackReactionOutput {
  ok: boolean;
  error?: string;
}

export interface SlackSearchMessagesInput {
  query: string;
  sort?: string;
  sort_dir?: string;
  count?: number;
  page?: number;
}

export interface SlackSearchMatch {
  iid: string;
  team: string;
  channel: Record<string, any>;
  type: string;
  user: string;
  username: string;
  ts: string;
  text: string;
  permalink: string;
}

export interface SlackSearchResultList {
  ok: boolean;
  query: string;
  messages: Record<string, any>;
  error?: string;
}

export interface SlackGetPermalinkInput {
  channel: string;
  message_ts: string;
}

export interface SlackPermalinkOutput {
  ok: boolean;
  permalink?: string;
  channel?: string;
  error?: string;
}

export interface SlackUpdateMessageInput {
  channel: string;
  ts: string;
  text: string;
}

export interface SlackUpdateMessageOutput {
  ok: boolean;
  channel?: string;
  ts?: string;
  text?: string;
  error?: string;
}

export interface XSocialPostInput {
  text: string;
  reply_to?: string;
  quote?: string;
}
// ------ /Models

// ------ SDK

import type { Nango } from '@nangohq/node';
import type {
  AxiosInstance,
  AxiosInterceptorManager,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import type {
  ApiEndUser,
  DBSyncConfig,
  DBTeam,
  GetPublicIntegration,
  HTTP_METHOD,
  RunnerFlags,
  PostPublicTrigger,
} from '@nangohq/types';
import type { ZodSchema, SafeParseSuccess } from 'zod';

export declare const oldLevelToNewLevel: {
  readonly debug: 'debug';
  readonly info: 'info';
  readonly warn: 'warn';
  readonly error: 'error';
  readonly verbose: 'debug';
  readonly silly: 'debug';
  readonly http: 'info';
};
type LogLevel = 'info' | 'debug' | 'error' | 'warn' | 'http' | 'verbose' | 'silly';
interface Pagination {
  type: string;
  limit?: number;
  response_path?: string;
  limit_name_in_request: string;
  in_body?: boolean;
  on_page?: (paginationState: {
    nextPageParam?: string | number | undefined;
    response: AxiosResponse;
  }) => Promise<void>;
}
interface CursorPagination extends Pagination {
  cursor_path_in_response: string;
  cursor_name_in_request: string;
}
interface LinkPagination extends Pagination {
  link_rel_in_response_header?: string;
  link_path_in_response_body?: string;
}
interface OffsetPagination extends Pagination {
  offset_name_in_request: string;
  offset_start_value?: number;
  offset_calculation_method?: 'per-page' | 'by-response-size';
}
interface RetryHeaderConfig {
  at?: string;
  after?: string;
}
export interface ProxyConfiguration {
  endpoint: string;
  providerConfigKey?: string;
  connectionId?: string;
  method?:
    | 'GET'
    | 'POST'
    | 'PATCH'
    | 'PUT'
    | 'DELETE'
    | 'get'
    | 'post'
    | 'patch'
    | 'put'
    | 'delete';
  headers?: Record<string, string>;
  params?: string | Record<string, string | number>;
  data?: unknown;
  retries?: number;
  baseUrlOverride?: string;
  paginate?: Partial<CursorPagination> | Partial<LinkPagination> | Partial<OffsetPagination>;
  retryHeader?: RetryHeaderConfig;
  responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream' | undefined;
  retryOn?: number[] | null;
}
export interface AuthModes {
  OAuth1: 'OAUTH1';
  OAuth2: 'OAUTH2';
  OAuth2CC: 'OAUTH2_CC';
  Basic: 'BASIC';
  ApiKey: 'API_KEY';
  AppStore: 'APP_STORE';
  Custom: 'CUSTOM';
  App: 'APP';
  None: 'NONE';
  TBA: 'TBA';
  Tableau: 'TABLEAU';
  Jwt: 'JWT';
  Bill: 'BILL';
  TwoStep: 'TWO_STEP';
  Signature: 'SIGNATURE';
}
export type AuthModeType = AuthModes[keyof AuthModes];
interface OAuth1Token {
  oAuthToken: string;
  oAuthTokenSecret: string;
}
interface AppCredentials {
  type: AuthModes['App'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
}
interface AppStoreCredentials {
  type?: AuthModes['AppStore'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
  private_key: string;
}
interface BasicApiCredentials {
  type: AuthModes['Basic'];
  username: string;
  password: string;
}
interface ApiKeyCredentials {
  type: AuthModes['ApiKey'];
  apiKey: string;
}
interface CredentialsCommon<T = Record<string, any>> {
  type: AuthModeType;
  raw: T;
}
interface OAuth2Credentials extends CredentialsCommon {
  type: AuthModes['OAuth2'];
  access_token: string;
  refresh_token?: string;
  expires_at?: Date | undefined;
}
interface OAuth2ClientCredentials extends CredentialsCommon {
  type: AuthModes['OAuth2CC'];
  token: string;
  expires_at?: Date | undefined;
  client_id: string;
  client_secret: string;
}
interface OAuth1Credentials extends CredentialsCommon {
  type: AuthModes['OAuth1'];
  oauth_token: string;
  oauth_token_secret: string;
}
interface TbaCredentials {
  type: AuthModes['TBA'];
  token_id: string;
  token_secret: string;
  config_override: {
    client_id?: string;
    client_secret?: string;
  };
}
interface TableauCredentials extends CredentialsCommon {
  type: AuthModes['Tableau'];
  pat_name: string;
  pat_secret: string;
  content_url?: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface JwtCredentials {
  type: AuthModes['Jwt'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface BillCredentials extends CredentialsCommon {
  type: AuthModes['Bill'];
  username: string;
  password: string;
  organization_id: string;
  dev_key: string;
  session_id?: string;
  user_id?: string;
  expires_at?: Date | undefined;
}
interface TwoStepCredentials extends CredentialsCommon {
  type: AuthModes['TwoStep'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface SignatureCredentials {
  type: AuthModes['Signature'];
  username: string;
  password: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface CustomCredentials extends CredentialsCommon {
  type: AuthModes['Custom'];
}
type UnauthCredentials = Record<string, never>;
type AuthCredentials =
  | OAuth2Credentials
  | OAuth2ClientCredentials
  | OAuth1Credentials
  | BasicApiCredentials
  | ApiKeyCredentials
  | AppCredentials
  | AppStoreCredentials
  | UnauthCredentials
  | TbaCredentials
  | TableauCredentials
  | JwtCredentials
  | BillCredentials
  | TwoStepCredentials
  | SignatureCredentials
  | CustomCredentials;
type Metadata = Record<string, unknown>;
interface MetadataChangeResponse {
  metadata: Metadata;
  provider_config_key: string;
  connection_id: string | string[];
}
interface Connection {
  id: number;
  provider_config_key: string;
  connection_id: string;
  connection_config: Record<string, string>;
  created_at: string;
  updated_at: string;
  last_fetched_at: string;
  metadata: Record<string, unknown> | null;
  provider: string;
  errors: {
    type: string;
    log_id: string;
  }[];
  end_user: ApiEndUser | null;
  credentials: AuthCredentials;
}
export declare class ActionError<T = Record<string, unknown>> extends Error {
  type: string;
  payload?: Record<string, unknown>;
  constructor(payload?: T);
}
export interface NangoProps {
  scriptType: 'sync' | 'action' | 'webhook' | 'on-event';
  host?: string;
  secretKey: string;
  team?: Pick<DBTeam, 'id' | 'name'>;
  connectionId: string;
  environmentId: number;
  environmentName?: string;
  activityLogId?: string | undefined;
  providerConfigKey: string;
  provider: string;
  lastSyncDate?: Date;
  syncId?: string | undefined;
  nangoConnectionId?: number;
  syncJobId?: number | undefined;
  dryRun?: boolean;
  track_deletes?: boolean;
  attributes?: object | undefined;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]> | undefined;
  rawDeleteOutput?: Map<string, unknown[]> | undefined;
  stubbedMetadata?: Metadata | undefined;
  abortSignal?: AbortSignal;
  syncConfig: DBSyncConfig;
  runnerFlags: RunnerFlags;
  debug: boolean;
  startedAt: Date;
  endUser: {
    id: number;
    endUserId: string | null;
    orgId: string | null;
  } | null;
  axios?: {
    request?: AxiosInterceptorManager<AxiosRequestConfig>;
    response?: {
      onFulfilled: (value: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
      onRejected: (value: unknown) => AxiosError | Promise<AxiosError>;
    };
  };
}
export interface EnvironmentVariable {
  name: string;
  value: string;
}
export declare const defaultPersistApi: AxiosInstance;
export declare class NangoAction {
  protected nango: Nango;
  private attributes;
  protected persistApi: AxiosInstance;
  activityLogId?: string | undefined;
  syncId?: string;
  nangoConnectionId?: number;
  environmentId: number;
  environmentName?: string;
  syncJobId?: number;
  dryRun?: boolean;
  abortSignal?: AbortSignal;
  syncConfig?: DBSyncConfig;
  runnerFlags: RunnerFlags;
  connectionId: string;
  providerConfigKey: string;
  provider?: string;
  ActionError: typeof ActionError;
  private memoizedConnections;
  private memoizedIntegration;
  constructor(
    config: NangoProps,
    {
      persistApi,
    }?: {
      persistApi: AxiosInstance;
    }
  );
  protected stringify(): string;
  private proxyConfig;
  protected throwIfAborted(): void;
  proxy<T = any>(config: ProxyConfiguration): Promise<AxiosResponse<T>>;
  get<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  post<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  put<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  patch<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  delete<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  getToken(): Promise<
    | string
    | OAuth1Token
    | OAuth2ClientCredentials
    | BasicApiCredentials
    | ApiKeyCredentials
    | AppCredentials
    | AppStoreCredentials
    | UnauthCredentials
    | CustomCredentials
    | TbaCredentials
    | TableauCredentials
    | JwtCredentials
    | BillCredentials
    | TwoStepCredentials
    | SignatureCredentials
  >;
  /**
   * Get current integration
   */
  getIntegration(
    queries?: GetPublicIntegration['Querystring']
  ): Promise<GetPublicIntegration['Success']['data']>;
  getConnection(
    providerConfigKeyOverride?: string,
    connectionIdOverride?: string
  ): Promise<Connection>;
  setMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  updateMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  /**
   * @deprecated please use setMetadata instead.
   */
  setFieldMapping(fieldMapping: Record<string, string>): Promise<AxiosResponse<object>>;
  getMetadata<T = Metadata>(): Promise<T>;
  getWebhookURL(): Promise<string | null | undefined>;
  /**
   * @deprecated please use getMetadata instead.
   */
  getFieldMapping(): Promise<Metadata>;
  /**
   * Log
   * @desc Log a message to the activity log which shows up in the Nango Dashboard
   * note that the last argument can be an object with a level property to specify the log level
   * @example
   * ```ts
   * await nango.log('This is a log message', { level: 'error' })
   * ```
   */
  log(
    message: any,
    options?:
      | {
          level?: LogLevel;
        }
      | {
          [key: string]: any;
          level?: never;
        }
  ): Promise<void>;
  log(
    message: string,
    ...args: [
      any,
      {
        level?: LogLevel;
      },
    ]
  ): Promise<void>;
  getEnvironmentVariables(): Promise<EnvironmentVariable[] | null>;
  getFlowAttributes<A = object>(): A | null;
  paginate<T = any>(config: ProxyConfiguration): AsyncGenerator<T[], undefined, void>;
  triggerAction<In = unknown, Out = object>(
    providerConfigKey: string,
    connectionId: string,
    actionName: string,
    input?: In
  ): Promise<Out>;
  zodValidateInput<T = any, Z = any>({
    zodSchema,
    input,
  }: {
    zodSchema: ZodSchema<Z>;
    input: T;
  }): Promise<SafeParseSuccess<Z>>;
  triggerSync(
    providerConfigKey: string,
    connectionId: string,
    sync: string | { name: string; variant: string },
    syncMode?: PostPublicTrigger['Body']['sync_mode'] | boolean
  ): Promise<void | string>;
  startSync(
    providerConfigKey: string,
    syncs: (string | { name: string; variant: string })[],
    connectionId?: string
  ): Promise<void>;
  /**
   * Uncontrolled fetch is a regular fetch without retry or credentials injection.
   * Only use that method when you want to access resources that are unrelated to the current connection/provider.
   */
  uncontrolledFetch(options: {
    url: URL;
    method?: HTTP_METHOD;
    headers?: Record<string, string> | undefined;
    body?: string | null;
  }): Promise<Response>;
  tryAcquireLock(props: { key: string; ttlMs: number }): Promise<boolean>;
  releaseLock(props: { key: string }): Promise<boolean>;
  private sendLogToPersist;
  private logAPICall;
}
export declare class NangoSync extends NangoAction {
  variant: string;
  lastSyncDate?: Date;
  track_deletes: boolean;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]>;
  rawDeleteOutput?: Map<string, unknown[]>;
  stubbedMetadata?: Metadata | undefined;
  private batchSize;
  constructor(config: NangoProps);
  /**
   * @deprecated please use batchSave
   */
  batchSend<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchSave<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchDelete<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchUpdate<T extends object>(results: T[], model: string): Promise<boolean | null>;
  getMetadata<T = Metadata>(): Promise<T>;
  setMergingStrategy(
    merging: { strategy: 'ignore_if_modified_after' | 'override' },
    model: string
  ): Promise<void>;
  getRecordsByIds<K = string | number, T = any>(ids: K[], model: string): Promise<Map<K, T>>;
}
/**
 * @internal
 *
 * This function will enable tracing on the SDK
 * It has been split from the actual code to avoid making the code too dirty and to easily enable/disable tracing if there is an issue with it
 */
export declare function instrumentSDK(rawNango: NangoAction | NangoSync): NangoAction | NangoSync;
export {};

// ------ /SDK

// ------ Flows
export const NangoFlows = [
  {
    providerConfigKey: 'google-mail',
    syncs: [
      {
        name: 'emails-fork',
        type: 'sync',
        description:
          'Fetches a list of emails from Gmail. Defaults to 1-day backfill,\nadjustable via `backfillPeriodMs` in milliseconds.',
        sync_type: 'incremental',
        usedModels: ['GmailEmail', 'Attachments', 'OptionalBackfillSetting'],
        runs: 'every 1 minutes',
        version: '1.0.4',
        track_deletes: false,
        auto_start: true,
        input: 'OptionalBackfillSetting',
        output: ['GmailEmail'],
        scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
        endpoints: [
          {
            method: 'GET',
            path: '/emails-fork',
            group: 'Emails',
          },
        ],
        webhookSubscriptions: [],
      },
      {
        name: 'emails-labels-added',
        type: 'sync',
        description:
          'Tracks emails where labels have been added using Gmail History API.\nStores historyId in metadata for incremental syncing.',
        sync_type: 'incremental',
        usedModels: ['GmailEmailLabelTriggered', 'Attachments'],
        runs: 'every 1 minutes',
        version: '1.0.0',
        track_deletes: false,
        auto_start: true,
        input: null,
        output: ['GmailEmailLabelTriggered'],
        scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
        endpoints: [
          {
            method: 'GET',
            path: '/labels-added',
            group: 'Emails',
          },
        ],
        webhookSubscriptions: [],
      },
    ],
    actions: [
      {
        name: 'compose-draft',
        type: 'action',
        description: 'Creates a new draft email in Gmail.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.modify'],
        input: 'GmailDraftInput',
        output: ['GmailDraftOutput'],
        usedModels: ['GmailDraftOutput', 'GmailDraftInput'],
        endpoint: {
          method: 'POST',
          path: '/draft',
        },
      },
      {
        name: 'compose-draft-reply',
        type: 'action',
        description: 'Creates a new draft email that is a reply to an existing email.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.modify'],
        input: 'GmailReplyDraftInput',
        output: ['GmailReplyDraftOutput'],
        usedModels: ['GmailReplyDraftOutput', 'GmailReplyDraftInput'],
        endpoint: {
          method: 'POST',
          path: '/draft-reply',
        },
      },
      {
        name: 'list-messages',
        type: 'action',
        description: 'Lists emails from Gmail inbox with optional filtering.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
        input: 'GmailListMessagesInput',
        output: ['GmailMessageList'],
        usedModels: ['GmailMessageList', 'GmailBasicMessageDetails', 'GmailListMessagesInput'],
        endpoint: {
          method: 'GET',
          path: '/list-messages',
        },
      },
      {
        name: 'get-message',
        type: 'action',
        description: 'Retrieves a specific email by ID.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
        input: 'GmailGetMessageInput',
        output: ['GmailMessage'],
        usedModels: ['GmailMessage', 'GmailHeader', 'GmailAttachmentInfo', 'GmailGetMessageInput'],
        endpoint: {
          method: 'GET',
          path: '/get-message',
        },
      },
      {
        name: 'send-email',
        type: 'action',
        description: 'Sends an email via Gmail.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.send'],
        input: 'GmailSendEmailInput',
        output: ['GmailSendEmailOutput'],
        usedModels: ['GmailSendEmailOutput', 'GmailSendEmailInput', 'GmailAttachment'],
        endpoint: {
          method: 'POST',
          path: '/send-email',
        },
      },
      {
        name: 'modify-message-labels',
        type: 'action',
        description: 'Modifies the labels applied to a specific message.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.modify'],
        input: 'GmailModifyMessageLabelsInput',
        output: ['GmailMessage'],
        usedModels: [
          'GmailMessage',
          'GmailHeader',
          'GmailAttachmentInfo',
          'GmailModifyMessageLabelsInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/gmail/v1/users/me/messages/:messageId/modify',
        },
      },
      {
        name: 'trash-message',
        type: 'action',
        description: 'Moves the specified message to the trash.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.modify'],
        input: 'GmailMessageIdInput',
        output: ['GmailMessage'],
        usedModels: ['GmailMessage', 'GmailHeader', 'GmailAttachmentInfo', 'GmailMessageIdInput'],
        endpoint: {
          method: 'POST',
          path: '/gmail/v1/users/me/messages/:messageId/trash',
        },
      },
      {
        name: 'untrash-message',
        type: 'action',
        description: 'Removes the specified message from the trash.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.modify'],
        input: 'GmailMessageIdInput',
        output: ['GmailMessage'],
        usedModels: ['GmailMessage', 'GmailHeader', 'GmailAttachmentInfo', 'GmailMessageIdInput'],
        endpoint: {
          method: 'POST',
          path: '/gmail/v1/users/me/messages/:messageId/untrash',
        },
      },
      {
        name: 'delete-message',
        type: 'action',
        description: 'Permanently deletes the specified message. Bypasses Trash.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/gmail.modify'],
        input: 'GmailMessageIdInput',
        output: ['GmailDeleteMessageOutput'],
        usedModels: ['GmailDeleteMessageOutput', 'GmailMessageIdInput'],
        endpoint: {
          method: 'DELETE',
          path: '/gmail/v1/users/me/messages/:messageId',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'x-social',
    syncs: [
      {
        name: 'user-tweets',
        type: 'sync',
        description:
          "Fetches tweets from a user's timeline. Initially fetches the last 10 tweets,\nthen uses since_id and until_id to track new tweets incrementally.",
        sync_type: 'incremental',
        usedModels: ['TwitterPost', 'ReferencedTweet', 'TweetMetrics', 'TwitterUserInput'],
        runs: 'every 15 minutes',
        version: '1.0.0',
        track_deletes: false,
        auto_start: true,
        input: 'TwitterUserInput',
        output: ['TwitterPost'],
        scopes: ['tweet.read', 'users.read'],
        endpoints: [
          {
            method: 'GET',
            path: '/user-tweets',
            group: 'Tweets',
          },
        ],
        webhookSubscriptions: [],
      },
      {
        name: 'user-mentions',
        type: 'sync',
        description:
          'Fetches tweets that mention the authenticated user. Initially fetches the last 10 mentions,\nthen uses since_id to track new mentions incrementally.',
        sync_type: 'incremental',
        usedModels: ['TwitterMention', 'ReferencedTweet', 'TweetMetrics', 'Mention'],
        runs: 'every 15 minutes',
        version: '1.0.0',
        track_deletes: false,
        auto_start: true,
        input: null,
        output: ['TwitterMention'],
        scopes: ['tweet.read', 'users.read'],
        endpoints: [
          {
            method: 'GET',
            path: '/user-mentions',
            group: 'Tweets',
          },
        ],
        webhookSubscriptions: [],
      },
    ],
    actions: [
      {
        name: 'send-post',
        type: 'action',
        description: 'Sends a new post to X.',
        version: '',
        scopes: ['tweet.read', 'tweet.write', 'users.read'],
        input: 'XSocialPostInput',
        output: ['XSocialPostOutput'],
        usedModels: ['XSocialPostOutput', 'XSocialPostInput'],
        endpoint: {
          method: 'POST',
          path: '/send-post',
        },
      },
      {
        name: 'get-user-profile',
        type: 'action',
        description: "Gets the authenticated user's profile information from X.",
        version: '',
        scopes: ['users.read'],
        input: null,
        output: ['XSocialUserProfile'],
        usedModels: ['XSocialUserProfile'],
        endpoint: {
          method: 'GET',
          path: '/get-user-profile',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'dropbox',
    syncs: [],
    actions: [
      {
        name: 'list-files',
        type: 'action',
        description: 'List files and folders in a Dropbox directory',
        version: '',
        scopes: ['files.metadata.read'],
        input: 'DropboxListFilesInput',
        output: ['DropboxFileList'],
        usedModels: ['DropboxFileList', 'DropboxEntry', 'DropboxListFilesInput'],
        endpoint: {
          method: 'GET',
          path: '/list-files',
        },
      },
      {
        name: 'get-file',
        type: 'action',
        description: 'Get file metadata and a download URL (not the actual file content)',
        version: '',
        scopes: ['files.metadata.read', 'files.content.read'],
        input: 'DropboxGetFileInput',
        output: ['DropboxFile'],
        usedModels: ['DropboxFile', 'DropboxGetFileInput'],
        endpoint: {
          method: 'GET',
          path: '/get-file',
        },
      },
      {
        name: 'upload-file',
        type: 'action',
        description: 'Upload string content as a file to Dropbox',
        version: '',
        scopes: ['files.metadata.write', 'files.content.write'],
        input: 'DropboxUploadFileInput',
        output: ['DropboxFile'],
        usedModels: ['DropboxFile', 'DropboxUploadFileInput'],
        endpoint: {
          method: 'POST',
          path: '/upload-file',
        },
      },
      {
        name: 'create-folder',
        type: 'action',
        description: 'Create a new folder in Dropbox and return folder metadata',
        version: '',
        scopes: ['files.metadata.write'],
        input: 'DropboxCreateFolderInput',
        output: ['DropboxFolder'],
        usedModels: ['DropboxFolder', 'DropboxCreateFolderInput'],
        endpoint: {
          method: 'POST',
          path: '/create-folder',
        },
      },
      {
        name: 'delete-file',
        type: 'action',
        description: 'Delete a file or folder in Dropbox and return metadata of the deleted item',
        version: '',
        scopes: ['files.content.write'],
        input: 'DropboxDeleteInput',
        output: ['DropboxDeleteResult'],
        usedModels: ['DropboxDeleteResult', 'DropboxEntry', 'DropboxDeleteInput'],
        endpoint: {
          method: 'POST',
          path: '/delete-file',
        },
      },
      {
        name: 'move-file',
        type: 'action',
        description:
          'Move a file or folder to a different location in Dropbox and return metadata of the moved item',
        version: '',
        scopes: ['files.content.write'],
        input: 'DropboxMoveInput',
        output: ['DropboxEntry'],
        usedModels: ['DropboxEntry', 'DropboxMoveInput'],
        endpoint: {
          method: 'POST',
          path: '/move-file',
        },
      },
      {
        name: 'copy-file',
        type: 'action',
        description:
          'Copy a file or folder to a different location in Dropbox and return metadata of the copied item',
        version: '',
        scopes: ['files.content.write'],
        input: 'DropboxCopyInput',
        output: ['DropboxEntry'],
        usedModels: ['DropboxEntry', 'DropboxCopyInput'],
        endpoint: {
          method: 'POST',
          path: '/copy-file',
        },
      },
      {
        name: 'search-files',
        type: 'action',
        description: 'Search for files and folders in Dropbox by filename or content',
        version: '',
        scopes: ['files.metadata.read'],
        input: 'DropboxSearchInput',
        output: ['DropboxSearchResult'],
        usedModels: [
          'DropboxSearchResult',
          'DropboxSearchMatch',
          'DropboxEntry',
          'DropboxSearchInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/search-files',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'github',
    syncs: [],
    actions: [
      {
        name: 'list-repositories',
        type: 'action',
        description: 'Lists repositories for the authenticated user.',
        version: '',
        scopes: ['repo'],
        input: null,
        output: ['GithubRepositoryList'],
        usedModels: ['GithubRepositoryList', 'GithubRepository', 'GithubIssueCreator'],
        endpoint: {
          method: 'GET',
          path: '/list-repositories',
        },
      },
      {
        name: 'get-repository',
        type: 'action',
        description: 'Gets a specific repository by owner and name.',
        version: '',
        scopes: ['repo'],
        input: 'GithubRepositoryInput',
        output: ['GithubRepository'],
        usedModels: ['GithubRepository', 'GithubIssueCreator', 'GithubRepositoryInput'],
        endpoint: {
          method: 'GET',
          path: '/get-repository',
        },
      },
      {
        name: 'create-repository',
        type: 'action',
        description:
          'Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url.',
        version: '',
        scopes: ['repo'],
        input: 'GithubCreateRepositoryInput',
        output: ['GithubRepository'],
        usedModels: ['GithubRepository', 'GithubIssueCreator', 'GithubCreateRepositoryInput'],
        endpoint: {
          method: 'POST',
          path: '/create-repository',
        },
      },
      {
        name: 'update-repository',
        type: 'action',
        description: 'Updates an existing repository.',
        version: '',
        scopes: ['repo'],
        input: 'GithubUpdateRepositoryInput',
        output: ['GithubRepository'],
        usedModels: ['GithubRepository', 'GithubIssueCreator', 'GithubUpdateRepositoryInput'],
        endpoint: {
          method: 'PATCH',
          path: '/update-repository',
        },
      },
      {
        name: 'delete-repository',
        type: 'action',
        description: 'Deletes a repository.',
        version: '',
        scopes: ['delete_repo'],
        input: 'GithubRepositoryInput',
        output: ['GithubDeleteRepositoryOutput'],
        usedModels: ['GithubDeleteRepositoryOutput', 'GithubRepositoryInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-repository',
        },
      },
      {
        name: 'create-organization-repository',
        type: 'action',
        description: 'Creates a new repository within a specified organization.',
        version: '',
        scopes: ['repo', 'admin:org'],
        input: 'GithubCreateOrganizationRepositoryInput',
        output: ['GithubRepository'],
        usedModels: [
          'GithubRepository',
          'GithubIssueCreator',
          'GithubCreateOrganizationRepositoryInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-organization-repository',
        },
      },
      {
        name: 'list-issues',
        type: 'action',
        description: 'Lists issues for a repository.',
        version: '',
        scopes: ['repo'],
        input: 'GithubIssuesInput',
        output: ['GithubIssueList'],
        usedModels: [
          'GithubIssueList',
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubIssuesInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-issues',
        },
      },
      {
        name: 'get-issue',
        type: 'action',
        description: 'Gets a specific issue by number.',
        version: '',
        scopes: ['repo'],
        input: 'GithubIssueInput',
        output: ['GithubIssue'],
        usedModels: [
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubIssueInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-issue',
        },
      },
      {
        name: 'create-issue',
        type: 'action',
        description: 'Creates a new issue in a repository.',
        version: '',
        scopes: ['repo'],
        input: 'GithubCreateIssueInput',
        output: ['GithubIssue'],
        usedModels: [
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubCreateIssueInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-issue',
        },
      },
      {
        name: 'update-issue',
        type: 'action',
        description: 'Updates an existing issue.',
        version: '',
        scopes: ['repo'],
        input: 'GithubUpdateIssueInput',
        output: ['GithubIssue'],
        usedModels: [
          'GithubIssue',
          'GithubIssueCreatorLite',
          'GithubIssueCreator',
          'GithubIssueLabel',
          'GithubSubIssuesSummary',
          'GithubReactions',
          'GithubUpdateIssueInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-issue',
        },
      },
      {
        name: 'get-pull-request',
        type: 'action',
        description: 'Get details of a specific pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubPullRequestInput',
        output: ['GithubPullRequest'],
        usedModels: [
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request',
        },
      },
      {
        name: 'update-pull-request',
        type: 'action',
        description: 'Update an existing pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubUpdatePullRequestInput',
        output: ['GithubPullRequest'],
        usedModels: [
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubUpdatePullRequestInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-pull-request',
        },
      },
      {
        name: 'list-pull-requests',
        type: 'action',
        description: 'List pull requests in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubListPullRequestsInput',
        output: ['GithubPullRequestList'],
        usedModels: [
          'GithubPullRequestList',
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubListPullRequestsInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-pull-requests',
        },
      },
      {
        name: 'merge-pull-request',
        type: 'action',
        description: 'Merge a pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubMergePullRequestInput',
        output: ['GithubMergeResult'],
        usedModels: ['GithubMergeResult', 'GithubMergePullRequestInput'],
        endpoint: {
          method: 'PUT',
          path: '/merge-pull-request',
        },
      },
      {
        name: 'get-pull-request-files',
        type: 'action',
        description: 'Get the files changed in a specific pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubPullRequestInput',
        output: ['GithubPullRequestFileList'],
        usedModels: [
          'GithubPullRequestFileList',
          'GithubPullRequestFile',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request-files',
        },
      },
      {
        name: 'get-pull-request-status',
        type: 'action',
        description: 'Get the combined status of all status checks for a pull request.',
        version: '',
        scopes: ['status'],
        input: 'GithubPullRequestInput',
        output: ['GithubCombinedStatus'],
        usedModels: [
          'GithubCombinedStatus',
          'GithubStatus',
          'GithubRepositoryForGithubCombinedStatus',
          'GithubIssueCreator',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request-status',
        },
      },
      {
        name: 'update-pull-request-branch',
        type: 'action',
        description:
          'Update the branch of a pull request with the latest changes from the base branch.',
        version: '',
        scopes: ['pull'],
        input: 'GithubUpdatePullRequestBranchInput',
        output: ['GithubBranchUpdateResult'],
        usedModels: ['GithubBranchUpdateResult', 'GithubUpdatePullRequestBranchInput'],
        endpoint: {
          method: 'PUT',
          path: '/update-pull-request-branch',
        },
      },
      {
        name: 'get-pull-request-comments',
        type: 'action',
        description: 'Get the review comments on a pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubPullRequestInput',
        output: ['GithubPullRequestCommentList'],
        usedModels: [
          'GithubPullRequestCommentList',
          'GithubPullRequestComment',
          'GithubIssueCreator',
          'GithubReactions',
          'GithubPullRequestInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-pull-request-comments',
        },
      },
      {
        name: 'add-pull-request-review-comment',
        type: 'action',
        description: 'Add a review comment to a pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubAddPullRequestReviewCommentInput',
        output: ['GithubPullRequestComment'],
        usedModels: [
          'GithubPullRequestComment',
          'GithubIssueCreator',
          'GithubReactions',
          'GithubAddPullRequestReviewCommentInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/add-pull-request-review-comment',
        },
      },
      {
        name: 'create-pull-request-review',
        type: 'action',
        description: 'Submit a review on a pull request.',
        version: '',
        scopes: ['pull'],
        input: 'GithubCreatePullRequestReviewInput',
        output: ['GithubPullRequestReview'],
        usedModels: [
          'GithubPullRequestReview',
          'GithubIssueCreator',
          'GithubCreatePullRequestReviewInput',
          'GithubDraftReviewComment',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-pull-request-review',
        },
      },
      {
        name: 'create-pull-request',
        type: 'action',
        description: 'Create a new pull request in a GitHub repository.',
        version: '',
        scopes: ['pull'],
        input: 'GithubCreatePullRequestInput',
        output: ['GithubPullRequest'],
        usedModels: [
          'GithubPullRequest',
          'GithubIssueCreator',
          'GithubIssueAssignee',
          'GithubTeamRef',
          'GithubIssueLabel',
          'GithubCreatePullRequestInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-pull-request',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'google-calendar',
    syncs: [],
    actions: [
      {
        name: 'create-event',
        type: 'action',
        description:
          'Creates a new event in Google Calendar. Can either be full-day or time-based.\n- summary: Event title / name.\n- description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.\n- location: Free form text.\n- start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.\n- end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.\n- timeZone: An IANA Time Zone e.g. (Area/City)\n- attendees: A list of attendee email addresses.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
        input: 'GoogleCalendarEventInput',
        output: ['GoogleCalendarEventOutput'],
        usedModels: ['GoogleCalendarEventOutput', 'GoogleCalendarEventInput'],
        endpoint: {
          method: 'POST',
          path: '/create-event',
        },
      },
      {
        name: 'update-event',
        type: 'action',
        description:
          'Updates an event in Google Calendar.\n- calendarId: Calendar identifier. Use "primary" unless otherwise advised.\n- eventId: Event identifier.\n- sendUpdates: Whether to send notifications about the event update.\n- summary: Event title / name.\n- description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.\n- location: Free form text.\n- start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.\n- end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.\n- timeZone: An IANA Time Zone e.g. (Area/City)\n- attendees: A list of attendee email addresses.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
        input: 'GoogleCalendarEventUpdateInput',
        output: ['GoogleCalendarEventOutput'],
        usedModels: [
          'GoogleCalendarEventOutput',
          'GoogleCalendarEventUpdateInput',
          'GoogleCalendarAttendeeInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-event',
        },
      },
      {
        name: 'list-calendars',
        type: 'action',
        description:
          'Lists all calendars available to the authenticated user.\nAVOID USING THIS ENDPOINT; Normally you can just pass "primary" as the calendarId to other endpoints, unless explicitly requested otherwise.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/calendar.readonly'],
        input: null,
        output: ['GoogleCalendarList'],
        usedModels: ['GoogleCalendarList'],
        endpoint: {
          method: 'GET',
          path: '/list-calendars',
        },
      },
      {
        name: 'list-events',
        type: 'action',
        description:
          'Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past.\n- calendarId: Calendar identifier. Use "primary" unless otherwise advised.\n- timeMin: Lower bound (inclusive) for an event\'s end time to filter by. Defaults to now. ISO8601 string format.\n- timeMax: Upper bound (exclusive) for an event\'s start time to filter by. Defaults to unbounded. ISO8601 string format.\n- maxResults: Defaults to 250. Max 2500.\n- pageToken: Token as per a previous response to get another page of results.\n- q: Free text search terms to find events that match these terms.\n- timeZone: Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/calendar.readonly'],
        input: 'GoogleCalendarEventsInput',
        output: ['GoogleCalendarEventList'],
        usedModels: ['GoogleCalendarEventList', 'GoogleCalendarEventsInput'],
        endpoint: {
          method: 'GET',
          path: '/list-events',
        },
      },
      {
        name: 'delete-event',
        type: 'action',
        description:
          'Deletes an event from Google Calendar.\n- calendarId: Calendar identifier. Use "primary" unless otherwise advised.\n- eventId: Event identifier.\n- sendUpdates: Whether to send notifications about the deletion of the event.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
        input: 'GoogleCalendarEventDeleteInput',
        output: null,
        usedModels: ['GoogleCalendarEventDeleteInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-event',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'google-docs',
    syncs: [],
    actions: [
      {
        name: 'get-document',
        type: 'action',
        description: 'Retrieves a specific Google Document.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/documents.readonly'],
        input: 'GoogleDocsGetDocumentInput',
        output: ['GoogleDocsDocument'],
        usedModels: ['GoogleDocsDocument', 'GoogleDocsGetDocumentInput'],
        endpoint: {
          method: 'GET',
          path: '/v1/documents/:documentId',
        },
      },
      {
        name: 'create-document',
        type: 'action',
        description: 'Creates a blank Google Document.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/documents'],
        input: 'GoogleDocsCreateDocumentInput',
        output: ['GoogleDocsDocument'],
        usedModels: ['GoogleDocsDocument', 'GoogleDocsCreateDocumentInput'],
        endpoint: {
          method: 'POST',
          path: '/v1/documents',
        },
      },
      {
        name: 'update-document',
        type: 'action',
        description: 'Applies batch updates to a Google Document.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/documents'],
        input: 'GoogleDocsUpdateDocumentInput',
        output: ['GoogleDocsUpdateDocumentOutput'],
        usedModels: ['GoogleDocsUpdateDocumentOutput', 'GoogleDocsUpdateDocumentInput'],
        endpoint: {
          method: 'POST',
          path: '/v1/documents/:documentId:batchUpdate',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'google-drive',
    syncs: [],
    actions: [
      {
        name: 'list-documents',
        type: 'action',
        description:
          'Lists documents in Google Drive with optional filtering by folder ID and document type.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/drive.readonly'],
        input: 'ListDocumentsInput',
        output: ['GoogleDriveDocumentList'],
        usedModels: ['GoogleDriveDocumentList', 'GoogleDriveDocument', 'ListDocumentsInput'],
        endpoint: {
          method: 'GET',
          path: '/list-documents',
        },
      },
      {
        name: 'list-root-folders',
        type: 'action',
        description: 'Lists folders at the root level of Google Drive.',
        version: '',
        scopes: ['https://www.googleapis.com/auth/drive.readonly'],
        input: null,
        output: ['GoogleDriveFolderList'],
        usedModels: ['GoogleDriveFolderList', 'GoogleDriveFolder'],
        endpoint: {
          method: 'GET',
          path: '/list-root-folders',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'google-sheet',
    syncs: [],
    actions: [
      {
        name: 'create-sheet',
        type: 'action',
        description: 'Creates a new Google Sheet with optional initial data.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/spreadsheets',
          'https://www.googleapis.com/auth/drive.file',
        ],
        input: 'GoogleSheetCreateInput',
        output: ['GoogleSheetCreateOutput'],
        usedModels: [
          'GoogleSheetCreateOutput',
          'GoogleSheetCreateInput',
          'GoogleSheetTab',
          'SheetData',
          'SheetRow',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-sheet',
        },
      },
      {
        name: 'update-sheet',
        type: 'action',
        description: 'Updates an existing Google Sheet with new data.',
        version: '',
        scopes: [
          'https://www.googleapis.com/auth/spreadsheets',
          'https://www.googleapis.com/auth/drive.file',
        ],
        input: 'GoogleSheetUpdateInput',
        output: ['GoogleSheetUpdateOutput'],
        usedModels: [
          'GoogleSheetUpdateOutput',
          'GoogleSheetUpdateInput',
          'SheetUpdate',
          'SheetData',
          'SheetRow',
        ],
        endpoint: {
          method: 'POST',
          path: '/update-sheet',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'harvest',
    syncs: [],
    actions: [
      {
        name: 'list-time-entries',
        type: 'action',
        description: 'Lists time entries from Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntriesInput',
        output: ['HarvestTimeEntryList'],
        usedModels: [
          'HarvestTimeEntryList',
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestTimeEntriesInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-time-entries',
        },
      },
      {
        name: 'get-time-entry',
        type: 'action',
        description: 'Gets a specific time entry by ID.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestTimeEntryInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-time-entry',
        },
      },
      {
        name: 'add-historical-time-entry',
        type: 'action',
        description:
          'Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking.',
        version: '',
        scopes: [],
        input: 'HarvestAddHistoricalTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestAddHistoricalTimeEntryInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/add-historical-time-entry',
        },
      },
      {
        name: 'start-timer',
        type: 'action',
        description:
          'Starts a new timer for a task. Checks company settings for duration vs timestamp tracking.',
        version: '',
        scopes: [],
        input: 'HarvestStartTimerInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestStartTimerInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/start-timer',
        },
      },
      {
        name: 'stop-timer',
        type: 'action',
        description: 'Stops a running time entry in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestTimeEntryInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/stop-timer',
        },
      },
      {
        name: 'restart-timer',
        type: 'action',
        description: 'Restarts a stopped time entry in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestTimeEntryInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/restart-timer',
        },
      },
      {
        name: 'update-time-entry',
        type: 'action',
        description: 'Updates an existing time entry in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestUpdateTimeEntryInput',
        output: ['HarvestTimeEntry'],
        usedModels: [
          'HarvestTimeEntry',
          'HarvestUser',
          'HarvestClientInTimeEntry',
          'ProjectReference',
          'HarvestTaskInTimeEntry',
          'HarvestExternalReferenceInput',
          'HarvestUpdateTimeEntryInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-time-entry',
        },
      },
      {
        name: 'delete-time-entry',
        type: 'action',
        description: 'Deletes a time entry in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTimeEntryInput',
        output: ['HarvestDeleteTimeEntryOutput'],
        usedModels: ['HarvestDeleteTimeEntryOutput', 'HarvestTimeEntryInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-time-entry',
        },
      },
      {
        name: 'list-projects',
        type: 'action',
        description: 'Lists projects from Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestProjectsInput',
        output: ['HarvestProjectList'],
        usedModels: [
          'HarvestProjectList',
          'HarvestProject',
          'HarvestClientReference',
          'HarvestPaginationLinks',
          'HarvestProjectsInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-projects',
        },
      },
      {
        name: 'get-project',
        type: 'action',
        description: 'Gets a specific project by ID.',
        version: '',
        scopes: [],
        input: 'HarvestProjectInput',
        output: ['HarvestProject'],
        usedModels: ['HarvestProject', 'HarvestClientReference', 'HarvestProjectInput'],
        endpoint: {
          method: 'GET',
          path: '/get-project',
        },
      },
      {
        name: 'delete-project',
        type: 'action',
        description: 'Deletes a project in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestProjectInput',
        output: ['HarvestDeleteProjectOutput'],
        usedModels: ['HarvestDeleteProjectOutput', 'HarvestProjectInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-project',
        },
      },
      {
        name: 'create-project',
        type: 'action',
        description: 'Creates a new project in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestCreateProjectInput',
        output: ['HarvestProject'],
        usedModels: ['HarvestProject', 'HarvestClientReference', 'HarvestCreateProjectInput'],
        endpoint: {
          method: 'POST',
          path: '/v2/projects',
        },
      },
      {
        name: 'list-clients',
        type: 'action',
        description: 'Lists clients from Harvest.',
        version: '',
        scopes: [],
        input: null,
        output: ['HarvestClientList'],
        usedModels: ['HarvestClientList', 'HarvestClient'],
        endpoint: {
          method: 'GET',
          path: '/list-clients',
        },
      },
      {
        name: 'get-client',
        type: 'action',
        description: 'Gets a specific client by ID.',
        version: '',
        scopes: [],
        input: 'HarvestClientInput',
        output: ['HarvestClient'],
        usedModels: ['HarvestClient', 'HarvestClientInput'],
        endpoint: {
          method: 'GET',
          path: '/get-client',
        },
      },
      {
        name: 'create-client',
        type: 'action',
        description: 'Creates a new client in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestCreateClientInput',
        output: ['HarvestClient'],
        usedModels: ['HarvestClient', 'HarvestCreateClientInput'],
        endpoint: {
          method: 'POST',
          path: '/v2/clients',
        },
      },
      {
        name: 'list-tasks',
        type: 'action',
        description: 'Lists tasks from Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestTasksInput',
        output: ['HarvestTaskList'],
        usedModels: ['HarvestTaskList', 'HarvestTask', 'HarvestTasksInput'],
        endpoint: {
          method: 'GET',
          path: '/list-tasks',
        },
      },
      {
        name: 'list-project-tasks',
        type: 'action',
        description: 'Lists task assignments for a specific project in Harvest.',
        version: '',
        scopes: [],
        input: 'HarvestProjectTasksInput',
        output: ['HarvestProjectTaskList'],
        usedModels: [
          'HarvestProjectTaskList',
          'HarvestProjectTask',
          'ProjectReference',
          'TaskInAssignment',
          'PaginationLinks',
          'HarvestProjectTasksInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-project-tasks',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'linear',
    syncs: [],
    actions: [
      {
        name: 'list-issues',
        type: 'action',
        description: 'Lists issues from Linear.',
        version: '',
        scopes: [],
        input: 'LinearIssuesInput',
        output: ['LinearIssueList'],
        usedModels: [
          'LinearIssueList',
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'PageInfo',
          'LinearIssuesInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-issues',
        },
      },
      {
        name: 'get-issue',
        type: 'action',
        description: 'Gets a specific issue by ID.',
        version: '',
        scopes: [],
        input: 'LinearIssueInput',
        output: ['LinearIssue'],
        usedModels: [
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'LinearIssueInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/get-issue',
        },
      },
      {
        name: 'create-issue',
        type: 'action',
        description: 'Creates a new issue in Linear.',
        version: '',
        scopes: [],
        input: 'LinearCreateIssueInput',
        output: ['LinearIssue'],
        usedModels: [
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'LinearCreateIssueInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-issue',
        },
      },
      {
        name: 'update-issue',
        type: 'action',
        description: 'Updates an existing issue in Linear.',
        version: '',
        scopes: [],
        input: 'LinearUpdateIssueInput',
        output: ['LinearIssue'],
        usedModels: [
          'LinearIssue',
          'LinearState',
          'LinearUser',
          'LinearTeamBasic',
          'LinearProjectBasic',
          'LinearLabel',
          'LinearUpdateIssueInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-issue',
        },
      },
      {
        name: 'delete-issue',
        type: 'action',
        description: 'Deletes an issue in Linear.',
        version: '',
        scopes: [],
        input: 'LinearIssueInput',
        output: ['LinearDeleteIssueOutput'],
        usedModels: ['LinearDeleteIssueOutput', 'LinearIssueInput'],
        endpoint: {
          method: 'DELETE',
          path: '/delete-issue',
        },
      },
      {
        name: 'list-projects',
        type: 'action',
        description: 'List all projects from Linear',
        version: '',
        scopes: [],
        input: 'LinearProjectsInput',
        output: ['LinearProjectList'],
        usedModels: [
          'LinearProjectList',
          'LinearProject',
          'LinearUserBasic',
          'LinearTeamBasic',
          'PageInfo',
          'LinearProjectsInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/list-projects',
        },
      },
      {
        name: 'get-project',
        type: 'action',
        description: 'Gets a specific project by ID.',
        version: '',
        scopes: [],
        input: 'LinearProjectInput',
        output: ['LinearProject'],
        usedModels: ['LinearProject', 'LinearUserBasic', 'LinearTeamBasic', 'LinearProjectInput'],
        endpoint: {
          method: 'GET',
          path: '/get-project',
        },
      },
      {
        name: 'create-project',
        type: 'action',
        description: 'Creates a new project in Linear.',
        version: '',
        scopes: [],
        input: 'LinearCreateProjectInput',
        output: ['LinearProject'],
        usedModels: [
          'LinearProject',
          'LinearUserBasic',
          'LinearTeamBasic',
          'LinearCreateProjectInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/create-project',
        },
      },
      {
        name: 'update-project',
        type: 'action',
        description: 'Updates an existing project in Linear.',
        version: '',
        scopes: [],
        input: 'LinearUpdateProjectInput',
        output: ['LinearProject'],
        usedModels: [
          'LinearProject',
          'LinearUserBasic',
          'LinearTeamBasic',
          'LinearUpdateProjectInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/update-project',
        },
      },
      {
        name: 'list-teams',
        type: 'action',
        description: 'Lists teams from Linear.',
        version: '',
        scopes: [],
        input: 'LinearTeamsInput',
        output: ['LinearTeamList'],
        usedModels: ['LinearTeamList', 'LinearTeam', 'LinearUser', 'PageInfo', 'LinearTeamsInput'],
        endpoint: {
          method: 'GET',
          path: '/list-teams',
        },
      },
      {
        name: 'get-team',
        type: 'action',
        description: 'Gets a specific team by ID.',
        version: '',
        scopes: [],
        input: 'LinearTeamInput',
        output: ['LinearTeam'],
        usedModels: ['LinearTeam', 'LinearUser', 'LinearTeamInput'],
        endpoint: {
          method: 'GET',
          path: '/get-team',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'linkedin',
    syncs: [],
    actions: [
      {
        name: 'get-user-profile',
        type: 'action',
        description: "Gets the authenticated user's profile information from LinkedIn.",
        version: '',
        scopes: ['r_liteprofile', 'r_emailaddress'],
        input: null,
        output: ['LinkedInUserProfile'],
        usedModels: ['LinkedInUserProfile'],
        endpoint: {
          method: 'GET',
          path: '/get-user-profile',
        },
      },
      {
        name: 'send-post',
        type: 'action',
        description: 'Creates a new post on LinkedIn.',
        version: '',
        scopes: ['w_member_social', 'openid', 'profile'],
        input: 'LinkedInPostInput',
        output: ['LinkedInPostOutput'],
        usedModels: ['LinkedInPostOutput', 'LinkedInPostInput'],
        endpoint: {
          method: 'POST',
          path: '/send-post',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'notion',
    syncs: [],
    actions: [
      {
        name: 'search',
        type: 'action',
        description:
          'Searches pages and databases in Notion. IMPORTANT - Use "" to search for everything.',
        version: '',
        scopes: [],
        input: 'NotionSearchInput',
        output: ['NotionSearchOutput'],
        usedModels: [
          'NotionSearchOutput',
          'NotionPageOrDatabase',
          'NotionUserReference',
          'NotionCover',
          'NotionIcon',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionGenericObjectPlaceholder',
          'NotionSearchInput',
          'NotionSort',
          'NotionFilter',
        ],
        endpoint: {
          method: 'POST',
          path: '/search',
        },
      },
      {
        name: 'get-page',
        type: 'action',
        description: 'Retrieves a specific Notion Page object by its ID.',
        version: '',
        scopes: [],
        input: 'NotionGetPageInput',
        output: ['NotionPageOrDatabase'],
        usedModels: [
          'NotionPageOrDatabase',
          'NotionUserReference',
          'NotionCover',
          'NotionIcon',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionGetPageInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/pages/:pageId',
        },
      },
      {
        name: 'get-database',
        type: 'action',
        description: 'Retrieves a specific Notion Database object by its ID.',
        version: '',
        scopes: [],
        input: 'NotionGetDatabaseInput',
        output: ['NotionDatabase'],
        usedModels: [
          'NotionDatabase',
          'NotionUserReference',
          'NotionIcon',
          'NotionCover',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionGetDatabaseInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/databases/:databaseId',
        },
      },
      {
        name: 'query-database',
        type: 'action',
        description: 'Queries a Notion database for pages, with optional filters and sorts.',
        version: '',
        scopes: [],
        input: 'NotionQueryDatabaseInput',
        output: ['NotionQueryDatabaseOutput'],
        usedModels: [
          'NotionQueryDatabaseOutput',
          'NotionPageOrDatabase',
          'NotionUserReference',
          'NotionCover',
          'NotionIcon',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionGenericObjectPlaceholder',
          'NotionQueryDatabaseInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/databases/:databaseId/query',
        },
      },
      {
        name: 'create-database',
        type: 'action',
        description: 'Creates a new Notion database as a subpage of a specified page.',
        version: '',
        scopes: [],
        input: 'NotionCreateDatabaseInput',
        output: ['NotionDatabase'],
        usedModels: [
          'NotionDatabase',
          'NotionUserReference',
          'NotionIcon',
          'NotionCover',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionCreateDatabaseInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/databases',
        },
      },
      {
        name: 'create-page',
        type: 'action',
        description: 'Creates a new Notion page.',
        version: '',
        scopes: [],
        input: 'NotionCreatePageInput',
        output: ['NotionPageOrDatabase'],
        usedModels: [
          'NotionPageOrDatabase',
          'NotionUserReference',
          'NotionCover',
          'NotionIcon',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionCreatePageInput',
        ],
        endpoint: {
          method: 'POST',
          path: '/pages',
        },
      },
      {
        name: 'update-page',
        type: 'action',
        description:
          'Updates properties of an existing Notion page. ALSO USED TO "delete" a page, set archive to true.',
        version: '',
        scopes: [],
        input: 'NotionUpdatePageInput',
        output: ['NotionPageOrDatabase'],
        usedModels: [
          'NotionPageOrDatabase',
          'NotionUserReference',
          'NotionCover',
          'NotionIcon',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionUpdatePageInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/pages/:pageId',
        },
      },
      {
        name: 'update-database',
        type: 'action',
        description:
          'Updates properties of an existing Notion database. ALSO USED TO "delete" a database, set archive to true.',
        version: '',
        scopes: [],
        input: 'NotionUpdateDatabaseInput',
        output: ['NotionDatabase'],
        usedModels: [
          'NotionDatabase',
          'NotionUserReference',
          'NotionIcon',
          'NotionCover',
          'NotionParentReference',
          'NotionProperties',
          'NotionTitleProperty',
          'NotionRichText',
          'NotionRichTextContent',
          'NotionRichTextAnnotations',
          'NotionUpdateDatabaseInput',
        ],
        endpoint: {
          method: 'PATCH',
          path: '/databases/:databaseId',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
  {
    providerConfigKey: 'slack',
    syncs: [],
    actions: [
      {
        name: 'send_message_as_user',
        type: 'action',
        description: 'Sends a message to a Slack channel as the authenticated user.',
        version: '',
        scopes: ['chat:write'],
        input: 'SlackSendMessageInput',
        output: ['SlackSendMessageOutput'],
        usedModels: ['SlackSendMessageOutput', 'SlackSendMessageInput'],
        endpoint: {
          method: 'POST',
          path: '/chat.postMessage',
        },
      },
      {
        name: 'list-channels',
        type: 'action',
        description: 'Lists channels in Slack.',
        version: '',
        scopes: ['channels:read', 'groups:read', 'im:read', 'mpim:read'],
        input: 'SlackListChannelsInput',
        output: ['SlackChannelList'],
        usedModels: ['SlackChannelList', 'SlackChannel', 'SlackListChannelsInput'],
        endpoint: {
          method: 'GET',
          path: '/conversations.list',
        },
      },
      {
        name: 'get-channel-history',
        type: 'action',
        description: 'Retrieves message history for a specific channel.',
        version: '',
        scopes: ['channels:history', 'groups:history', 'im:history', 'mpim:history'],
        input: 'SlackGetChannelHistoryInput',
        output: ['SlackMessageList'],
        usedModels: [
          'SlackMessageList',
          'SlackMessage',
          'SlackBlock',
          'SlackAttachment',
          'SlackFile',
          'SlackReaction',
          'SlackEdited',
          'SlackResponseMetadata',
          'SlackGetChannelHistoryInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/conversations.history',
        },
      },
      {
        name: 'get-user-info',
        type: 'action',
        description: 'Retrieves information about a specific user.',
        version: '',
        scopes: ['users:read'],
        input: 'SlackGetUserInfoInput',
        output: ['SlackUserInfo'],
        usedModels: ['SlackUserInfo', 'SlackUserProfile', 'SlackGetUserInfoInput'],
        endpoint: {
          method: 'GET',
          path: '/users.info',
        },
      },
      {
        name: 'add_reaction_as_user',
        type: 'action',
        description: 'Adds an emoji reaction to a message as the authenticated user.',
        version: '',
        scopes: ['reactions:write'],
        input: 'SlackAddReactionInput',
        output: ['SlackReactionOutput'],
        usedModels: ['SlackReactionOutput', 'SlackAddReactionInput'],
        endpoint: {
          method: 'POST',
          path: '/reactions.add',
        },
      },
      {
        name: 'search-messages',
        type: 'action',
        description: 'Searches for messages matching a query.',
        version: '',
        scopes: ['search:read'],
        input: 'SlackSearchMessagesInput',
        output: ['SlackSearchResultList'],
        usedModels: ['SlackSearchResultList', 'SlackSearchMessagesInput'],
        endpoint: {
          method: 'GET',
          path: '/search.messages',
        },
      },
      {
        name: 'get-message-permalink',
        type: 'action',
        description: 'Retrieves a permalink for a specific message.',
        version: '',
        scopes: ['chat:read'],
        input: 'SlackGetPermalinkInput',
        output: ['SlackPermalinkOutput'],
        usedModels: ['SlackPermalinkOutput', 'SlackGetPermalinkInput'],
        endpoint: {
          method: 'GET',
          path: '/chat.getPermalink',
        },
      },
      {
        name: 'update_message_as_user',
        type: 'action',
        description: 'Updates an existing message in a channel as the authenticated user.',
        version: '',
        scopes: ['chat:write'],
        input: 'SlackUpdateMessageInput',
        output: ['SlackUpdateMessageOutput'],
        usedModels: ['SlackUpdateMessageOutput', 'SlackUpdateMessageInput'],
        endpoint: {
          method: 'POST',
          path: '/chat.update',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
] as const;
// ------ /Flows
