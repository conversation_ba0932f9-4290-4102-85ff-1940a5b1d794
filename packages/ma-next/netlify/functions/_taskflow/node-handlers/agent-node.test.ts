import { strictEqual, deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { z } from 'zod';
import { executeAgentNode, AgentNodeDependencies } from './agent-node';
import { NodeExecutionSubtypeParams } from '../types';
import { ACTION_INPUTS_KEYED, ACTION_OUTPUTS_KEYED } from '../../_agents/nangoConstants';

// setup fake schemas
const INPUT_SCHEMA = z.object({ foo: z.string() });
const OUTPUT_SCHEMA = z.object({ bar: z.string() });
(ACTION_INPUTS_KEYED as any)['testProvider:testAction'] = {
  model: 'TestInput',
  zodSchema: INPUT_SCHEMA,
  jsonSchema: { type: 'object', properties: { foo: { type: 'string' } }, required: ['foo'] },
};
(ACTION_OUTPUTS_KEYED as any)['testProvider:testAction'] = {
  model: 'TestOutput',
  zodSchema: OUTPUT_SCHEMA,
  jsonSchema: { type: 'object', properties: { bar: { type: 'string' } }, required: ['bar'] },
};

let genCalls: any[] = [];
const mockGenerateObject = async (opts: any) => {
  genCalls.push(opts);
  return { object: { foo: 'baz' } };
};

const deps: AgentNodeDependencies = {
  createOpenAI: () => ({ responses: () => ({}) }) as any,
  generateObject: mockGenerateObject,
  jsonSchema: (s: any) => s,
  getRunner: () => (n: any, p: any) => Promise.resolve({ bar: 'out', params: p }),
  getPseudoNangoAction: () => ({}) as any,
};

const mockSupabase = {
  from: () => ({
    select: () => ({
      eq: () => ({
        eq: () => ({ maybeSingle: () => Promise.resolve({ data: { id: 'c1' }, error: null }) }),
      }),
    }),
  }),
};

const params: NodeExecutionSubtypeParams = {
  node: {
    id: 'n1',
    type: 'agent.aToB',
    subtype: 'aToB',
    parameters: {
      system: 'sys',
      prompt: 'data',
      output: { providerKey: 'testProvider', actionKey: 'testAction' },
      userProvidedParameters: { extra: 'val' },
    },
  },
  supabase: mockSupabase as any,
  executionId: 'e1',
  userId: 'u1',
  userProfile: {},
};

test('agent aToB node executes', async () => {
  genCalls = [];
  const res = await executeAgentNode(params, deps);
  strictEqual(res.status, 'SUCCESS');
  strictEqual(genCalls.length, 1);
  deepStrictEqual((res.result as any).bar, 'out');
});
