import { createOpenAI } from '@ai-sdk/openai';
import { generateObject, jsonSchema } from 'ai';
import { NodeExecutionSubtypeParams, NodeOutput } from '../types';
import { getRunner } from '../../_tools/actions';
import { getPseudoNangoAction } from '../../_nango/getPseudoNangoAction';
import { ACTION_OUTPUTS_KEYED, ACTION_INPUTS_KEYED } from '../../_agents/nangoConstants';

const DEFAULT_DEPS: Required<AgentNodeDependencies> = {
  createOpenAI: () => {
    const apiKey = process.env.OPENAI_API_KEY;

    if (!apiKey) {
      throw new Error(
        'OpenAI API key is missing. Please set the OPENAI_API_KEY environment variable.'
      );
    }
    return createOpenAI({ apiKey });
  },
  generateObject,
  jsonSchema,
  getRunner,
  getPseudoNangoAction,
};

interface AgentNodeDependencies {
  createOpenAI?: typeof createOpenAI;
  generateObject?: typeof generateObject;
  jsonSchema?: typeof jsonSchema;
  getRunner?: typeof getRunner;
  getPseudoNangoAction?: typeof getPseudoNangoAction;
}

/**
 * Executes an agent node based on its subtype.
 */
async function executeAgentNode(
  execCtx: NodeExecutionSubtypeParams,
  deps: AgentNodeDependencies = {}
): Promise<NodeOutput> {
  const merged = { ...DEFAULT_DEPS, ...deps } as Required<AgentNodeDependencies>;
  const { subtype } = execCtx.node;

  switch (subtype) {
    case 'aToB':
      return executeAToBNode(execCtx, merged);
    default:
      return { status: 'ERROR', error: `Unsupported agent node subtype: ${subtype}` };
  }
}

/**
 * Executes an A to B agent node, transforming trigger data into parameters for an action.
 */
async function executeAToBNode(
  execCtx: NodeExecutionSubtypeParams,
  deps: Required<AgentNodeDependencies>
): Promise<NodeOutput> {
  const { generateObject, jsonSchema } = deps;

  const {
    node: {
      parameters: {
        system: userSystem = '',
        prompt = '',
        model = 'gpt-4.1',
        output,
        userProvidedParameters = {},
      } = {},
    },
    supabaseFacade,
    tracer,
    userId,
  } = execCtx;

  tracer.addStep('[executeAgentNode] start', { prompt, model, output });

  if (!output?.providerKey || !output?.actionKey) {
    tracer.addStep('[executeAgentNode] missing output providerKey or actionKey');
    return { status: 'ERROR', error: 'Missing output providerKey or actionKey' };
  }

  const actionKeyed = `${output.providerKey}:${output.actionKey}` as const;
  const outputInfo = (ACTION_OUTPUTS_KEYED as any)[actionKeyed];
  const inputInfo = (ACTION_INPUTS_KEYED as any)[actionKeyed];

  if (!outputInfo || !inputInfo) {
    tracer.addStep('[executeAgentNode] unsupported action', { actionKeyed });
    return { status: 'ERROR', error: `Unsupported action ${actionKeyed}` };
  }

  const openai = deps.createOpenAI();
  const llm = openai(model);

  const systemPrefix =
    `You are running a workflow step that transforms trigger data into parameters for the ${output.providerKey} ${output.actionKey} action.` +
    `The following instructions from the user describe how to perform this transformation.`;
  const system = `${systemPrefix}\n\n${userSystem}`;

  const schema = jsonSchema({
    ...inputInfo.jsonSchema,
    additionalProperties: false,
  });

  tracer.addStep('[executeAgentNode] system prompt', { system, schema });

  try {
    const { object: actionParams } = await generateObject({
      model: llm,
      prompt,
      system,
      schema,
    });

    tracer.addStep('[executeAgentNode] generated actionParams', { actionParams });

    inputInfo.zodSchema.parse(actionParams);

    // Perform action
    const { data: connection, error: connError } = await supabaseFacade.getConnection(
      output.providerKey,
      userId
    );

    if (connError || !connection) {
      tracer.addStep('[executeAgentNode] no connection found', { providerKey: output.providerKey });
      return { status: 'ERROR', error: `No connection found for provider: ${output.providerKey}` };
    }

    tracer.addStep('[executeAgentNode] connection found', { connectionId: connection.id });

    const nango = deps.getPseudoNangoAction(output.providerKey, connection.id);
    const runner = deps.getRunner(output.providerKey, output.actionKey);

    if (!runner) {
      tracer.addStep('[executeAgentNode] no handler found', { actionKeyed });
      return { status: 'ERROR', error: `No handler for ${actionKeyed}` };
    }

    const result = await runner(nango, actionParams);

    if ('error' in result) {
      tracer.addStep('[executeAgentNode] action error', { error: result.error });
      return { status: 'ERROR', error: result.error };
    }

    tracer.addStep('[executeAgentNode] action result: success', { result });

    return { status: 'SUCCESS', result };
  } catch (err: any) {
    tracer.addStep('[executeAgentNode] main try catch error', {
      error: err,
    });
    return {
      status: 'ERROR',
      error: err instanceof Error ? err.message : String(err),
    };
  }
}

export type { AgentNodeDependencies };
export { executeAgentNode };
