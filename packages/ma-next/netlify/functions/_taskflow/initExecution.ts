import { ExecTaskflowParams, ExecutionContext, PreparedData, WorkflowSchema } from './types';
import { DebugTracer } from './debugTracer';

/**
 * Prepares the taskflow execution by loading or creating the execution context
 *
 * @param params The request parameters
 * @returns [PreparedData, null] | [null, Error]
 */
async function initExecution({
  taskflowId,
  taskflowExecutionId,
  triggerData: passedTriggerData = {},
  resumeData,
  supabaseFacade: facade,
  userId,
  tracer,
}: ExecTaskflowParams): Promise<[PreparedData, null] | [null, Error]> {
  let executionId: string;
  let taskflowSchema: any;
  let resumedExecution: {
    id: string;
    context: ExecutionContext;
    triggerData: Record<string, any>;
  } | null = null;

  tracer.addStep('[initExecution] start', { taskflowId, taskflowExecutionId, resumeData, userId, resumedExecution });

  const profilePromise = facade.getProfile(userId);

  // Handle resume case
  if (resumeData?.executionId || taskflowExecutionId) {
    tracer.addStep('[initExecution] facade.getActiveExecution query');
    const { data: existingExecution } = await facade.getActiveExecution(
      (resumeData?.executionId || taskflowExecutionId)!
    );

    if (!existingExecution) {
      tracer.addStep('[initExecution] facade.getActiveExecution result: not found', {});
      return [null, new Error('Execution not found')];
    }

    resumedExecution = existingExecution;
    executionId = existingExecution.id;

    tracer.addStep('[initExecution] facade.getActiveExecution result: found', { resumedExecution, executionId });

    // Get the taskflow schema
    tracer.addStep('[initExecution] facade.getUserTaskflow query')
    const { data: taskflow, error: taskflowError } = await facade.getUserTaskflow(
      existingExecution.taskflowId,
      userId
    );

    if (taskflowError || !taskflow || !taskflow.taskflowSchema) {
      tracer.addStep('[initExecution] facade.getUserTaskflow result: not found', { taskflowError });
      return [
        null,
        taskflowError ? new Error(taskflowError.message) : new Error('Taskflow not found'),
      ];
    }

    taskflowSchema = taskflow.taskflowSchema;

    tracer.addStep('[initExecution] facade.getUserTaskflow result: found', { taskflowSchema });
  } // Handle creating a new execution
  else if (taskflowId) {
    // Get the taskflow schema
    tracer.addStep('[initExecution] facade.getUserTaskflow query');
    const { data: taskflow, error: taskflowError } = await facade.getUserTaskflow(
      taskflowId,
      userId
    );

    if (taskflowError || !taskflow || !taskflow.taskflowSchema) {
      tracer.addStep('[initExecution] facade.getUserTaskflow result: not found', { taskflowError });
      return [
        null,
        taskflowError ? new Error(taskflowError.message) : new Error('Taskflow not found'),
      ];
    }

    taskflowSchema = taskflow.taskflowSchema;
    tracer.addStep('[initExecution] facade.getUserTaskflow result: found', { taskflowSchema });

    // Create the execution
    tracer.addStep('[initExecution] facade.createExecution', { taskflowId, passedTriggerData });
    const { data: createExecution, error: createExecutionError } = await facade.createExecution(
      taskflowId,
      passedTriggerData
    );

    if (createExecutionError) {
      tracer.addStep('[initExecution] facade.createExecution error', { createExecutionError });
      return [null, new Error(createExecutionError.message)];
    }
    executionId = createExecution.id;

    tracer.addStep('[initExecution] facade.createExecution result: created', { executionId });
  } else {
    tracer.addStep('[initExecution] insufficient parameters');
    return [
      null,
      new Error('Either taskflowId, taskflowExecutionId, or resumeData must be provided'),
    ];
  }

  // Prepare execution context
  const { nodes: allNodes } = taskflowSchema as WorkflowSchema;
  const context: ExecutionContext = resumedExecution?.context || {};
  const triggerData = passedTriggerData || resumedExecution?.triggerData;
  const nodes = resumeData?.nodeId
    ? allNodes.slice(allNodes.findIndex(n => n.id === resumeData.nodeId))
    : allNodes;

  const preparedData: PreparedData = {
    taskflow: {
      schema: taskflowSchema,
      nodes,
    },
    taskflowExecution: {
      id: executionId,
      context,
      triggerData,
      resumeData,
    },
    userProfile: (await profilePromise).data ?? { id: userId },
  };

  tracer.addStep('[initExecution] prepared', { preparedData });

  return [preparedData, null] as [PreparedData, null] | [null, Error];
}

export { initExecution };
export type { PreparedData };
