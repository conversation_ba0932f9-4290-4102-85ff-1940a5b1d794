import { deepStrictEqual as assertEquals, deepStrictEqual as assertObjectMatch } from 'node:assert';
import { test } from 'node:test';
import { finaliseExecution } from '../finaliseExecution';
import { NodeStatus } from '../types';

// Mock Supabase client for testing
const mockSupabase = {
  from: (table: string) => ({
    update: (data: any) => ({
      eq: (_column: string, _value: any) =>
        Promise.resolve({
          data: data,
          error: null,
        }),
    }),
  }),
};

test('finaliseExecution - preserves HITL parameters in result', async () => {
  // Create a mock execution context with HITL parameters
  const executionId = 'execution-123';
  const context = {
    node1: {
      status: 'SUCCESS' as NodeStatus,
      type: 'ai.simple',
      output: { result: 'test output' },
      hitl: { type: 'form', parameters: { fields: [] } },
    },
    node2: {
      status: 'SUCCESS' as NodeStatus,
      type: 'ai.simple',
      output: { result: 'another output' },
    },
  };

  // Call finaliseExecution
  const [result, error] = await finaliseExecution(
    executionId,
    context,
    mockSupabase as any,
    'node2'
  );

  // Verify the result
  assertEquals(error, null);
  assertEquals(result?.executionId, executionId);

  // Check that the result contains the expected data
  const finalResult = result as any;

  // Verify that node1's HITL parameters are preserved
  assertEquals(finalResult.result.node1, {
    status: 'SUCCESS',
    type: 'ai.simple',
    output: { result: 'test output' },
    hitl: { type: 'form', parameters: { fields: [] } },
  });

  // Verify that node2's data is correct
  assertEquals(finalResult.result.node2, {
    status: 'SUCCESS',
    type: 'ai.simple',
    output: { result: 'another output' },
  });
});

test('finaliseExecution - handles error status correctly', async () => {
  // Create a mock execution context with an error
  const executionId = 'execution-123';
  const context = {
    node1: {
      status: 'SUCCESS' as NodeStatus,
      type: 'ai.simple',
      output: { result: 'test output' },
    },
    node2: {
      status: 'ERROR' as NodeStatus,
      type: 'ai.simple',
      error: 'Something went wrong',
      hitl: { type: 'form', parameters: { fields: [] } },
    },
  };

  // Call finaliseExecution
  const [result, error] = await finaliseExecution(
    executionId,
    context,
    mockSupabase as any,
    'node2'
  );

  // Verify the result
  assertEquals(error, null);
  assertEquals(result?.executionId, executionId);

  // Since the last node has ERROR status, we should get a result with errors
  const finalResult = result as any;

  // Check that errors are included
  assertObjectMatch(finalResult.errors, {
    node2: 'Something went wrong',
  });

  // Check that the result contains the expected data with HITL preserved
  assertEquals(finalResult.result.node1, {
    status: 'SUCCESS',
    type: 'ai.simple',
    output: { result: 'test output' },
  });

  assertObjectMatch(finalResult.result.node2, {
    status: 'ERROR',
    type: 'ai.simple',
    error: 'Something went wrong',
    hitl: { type: 'form', parameters: { fields: [] } },
  });
});
