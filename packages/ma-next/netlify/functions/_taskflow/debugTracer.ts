import fs from 'fs';
import { getDebugMode } from '../_shared/debug';
import { TaskflowSupabaseFacade } from './taskflowSupabaseFacade';

export class DebugTracer {
  private steps: { description: string; data: any }[] = [];
  private supabaseFacade: TaskflowSupabaseFacade;

  constructor(supabaseFacade: TaskflowSupabaseFacade) {
    this.supabaseFacade = supabaseFacade;
  }

  addStep(description: string, data: Record<string, any> = {}) {
    const cloned = JSON.parse(JSON.stringify(data));
    this.steps.push({ description, data: cloned });
  }

  getTrace() {
    return this.steps;
  }

  commit() {
    if (getDebugMode()) {
      fs.writeFileSync(
        `debug_trace.json`,
        JSON.stringify(this.steps, null, 2)
      );
    }
  }
}
