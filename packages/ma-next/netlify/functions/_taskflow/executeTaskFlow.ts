import { ExecTaskflowParams, ExecutionContext } from './types';
import { initExecution } from './initExecution';
import { executeNodes } from './executeNodes';
import { finaliseExecution } from './finaliseExecution';

type ExecutionResult = {
  executionId: string;
  context: ExecutionContext;
};

type FinalResult = {
  executionId: string;
  result: Record<string, any>;
  errors?: Record<string, any>;
};

type Result = ExecutionResult | FinalResult;

/**
 * Executes a taskflow (resume / trigger)
 */
async function executeTaskFlow(
  params: ExecTaskflowParams
): Promise<[Result, null] | [null, Error]> {
  const { tracer, supabaseFacade, ...dataParams } = params;

  tracer.addStep('[executeTaskFlow] initExecution start', { params: dataParams });

  // Step 1: Prepare the execution (resolves database context)
  const [prep, prepError] = await initExecution(params);

  if (prep === null || prepError !== null) {
    tracer.addStep('[executeTaskFlow] initExecution: error', { prepError });
    return [null, prepError || new Error('Failed to prepare execution')];
  }
  tracer.addStep('[executeTaskFlow] initExecution: success', { prep });

  // Step 2: Execute the nodes
  tracer.addStep('[executeTaskFlow] executeNodes start');
  const [exec, execError] = await executeNodes({
    prep,
    facade: params.supabaseFacade,
    userId: params.userId,
    tracer,
    handlers: params.handlers,
  });

  if (!exec || execError) {
    tracer.addStep('[executeTaskFlow] executeNodes error', { execError });
    return [null, execError || new Error('Failed to execute nodes')];
  }

  // Step 3: Finalize the execution
  const {
    taskflow: { nodes },
    taskflowExecution: { id: executionId },
  } = prep;

  const lastNodeId = nodes[nodes.length - 1].id;
  const [finalResult, finalError] = await finaliseExecution(
    executionId,
    exec.context,
    params.supabaseFacade,
    lastNodeId,
    tracer
  );
  if (finalError) {
    tracer.addStep('[executeTaskFlow] finalise error', { finalError });
    tracer.commit();
    return [null, finalError];
  }
  tracer.addStep('[executeTaskFlow] done', { finalResult });
  tracer.commit();
  return [finalResult, null];
}

export { executeTaskFlow };
export type { ExecutionResult, FinalResult };
