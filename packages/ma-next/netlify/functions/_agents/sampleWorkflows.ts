// This file contains workflow schemas for different agent types

/**
 * Email Assistant Workflow Schema
 * Triggers on new emails and drafts responses
 */
export const EMAIL_WORKFLOW_SCHEMA = {
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger.syncTrigger',
      parameters: {
        providerKey: 'google-mail',
        model: 'GmailEmail',
        syncKey: 'emails-fork',
        label: 'New Email Trigger',
        description: 'Triggers when a new email is received',
      },
      condition: { '!=': [{ var: 'isDraft' }, true] },
    },
    {
      id: 'node1',
      type: 'ai.simple',
      parameters: {
        system:
          "You are an email assistant that helps draft responses to incoming emails my behalf. Be professional and concise. Draft a response to the email presented below on my behalf. Use the sender's name in the greeting and include a closing signature with my name.",
        prompt:
          '\n\nFrom: {{trigger.sender}}\nSubject: {{trigger.subject}}\nBody: {{trigger.body}}\n\n',
        outputSchema: {
          type: 'object',
          properties: {
            subject: {
              type: 'string',
              description: 'The subject line for the response email',
            },
            body: {
              type: 'string',
              description: 'The body content of the response email',
            },
          },
          required: ['subject', 'body'],
        },
      },
    },
    {
      id: 'node2',
      type: 'provider.google-mail.compose-draft-reply',
      parameters: {
        replyBody: '{{node1.body}}',
        sender: '{{trigger.sender.match(/[^<]+<([^>]+)>/)?.[1] || trigger.sender}}',
        subject: '{{trigger.subject}}',
        body: '{{trigger.body}}',
        threadId: '{{trigger.threadId}}',
        messageId: '{{trigger.messageId}}',
        inReplyTo: '{{trigger.inReplyTo}}',
        references: '{{trigger.references}}',
        date: '{{trigger.date}}',
      },
    },
  ],
};

/**
 * GitHub PR Description Bot Workflow Schema
 * Triggers on new PRs and generates descriptions based on file changes
 */
export const GITHUB_PR_DESCRIPTION_WORKFLOW_SCHEMA = {
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger.syncTrigger',
      parameters: {
        providerKey: 'github',
        model: 'PullRequest',
        syncKey: 'pull-requests',
        label: 'New Pull Request Trigger',
        description: 'Triggers when a new pull request is created',
      },
    },
    {
      id: 'node1',
      type: 'provider.github.get-pull-request-files',
      parameters: {
        owner: '{{trigger.repository.owner.login}}',
        repo: '{{trigger.repository.name}}',
        pull_number: '{{trigger.number}}',
      },
    },
    {
      id: 'node2',
      type: 'ai.simple',
      parameters: {
        model: 'gpt-4o',
        system:
          'You are a technical writer who specializes in creating clear, concise pull request descriptions. Analyze the file changes and create a professional PR description that explains the changes made, their purpose, and any important implementation details.',
        prompt:
          "Generate a detailed pull request description based on the following file changes:\n\n{{node1.files.map(file => `File: ${file.filename}\\nStatus: ${file.status}\\nAdditions: ${file.additions}\\nDeletions: ${file.deletions}\\n${file.patch ? 'Patch: ' + file.patch : ''}\\n\\n`).join('')}}",
        outputSchema: {
          type: 'object',
          properties: {
            title: {
              type: 'string',
              description: 'A concise title for the pull request',
            },
            description: {
              type: 'string',
              description: 'A detailed description of the changes in the pull request',
            },
            changeType: {
              type: 'string',
              enum: ['feature', 'bugfix', 'refactor', 'documentation', 'other'],
              description: 'The type of change in this pull request',
            },
          },
          required: ['title', 'description', 'changeType'],
        },
      },
    },
    {
      id: 'node3',
      type: 'provider.github.update-pull-request',
      parameters: {
        owner: '{{trigger.repository.owner.login}}',
        repo: '{{trigger.repository.name}}',
        pull_number: '{{trigger.number}}',
        title: '{{node2.title}}',
        body: '{{node2.description}}',
        requiresConfirmation: true,
      },
    },
  ],
};

/**
 * Example A to B workflow schema using the new agent node
 */
export const A_TO_B_INSTANCE_SCHEMA = {
  nodes: [
    {
      id: "trigger1",
      type: "trigger.syncTrigger",
      parameters: {
        providerKey: "github",
        model: "GithubPullRequest",
        syncKey: "pull-requests",
        label: "New Pull Request Trigger",
        description: "Triggers when a new pull request is created",
      },
      condition: {},
    },
    {
      id: "node1",
      type: "agent.aToB",
      parameters: {
        system: "Change the title according to conventional commit standards in alignment with the PR description.",
        prompt: "Input data: {{ JSON.stringify(trigger) }}",
        output: {
          providerKey: "github",
          actionKey: "update-pull-request",
        },
        userProvidedParameters: {},
      },
    },
  ],
};
