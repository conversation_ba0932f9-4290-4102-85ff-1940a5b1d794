import { Nango } from '@nangohq/node';

type ProviderMetadata = {
  providerUserId: string;
  displayName: string;
  additionalMetadata?: Record<string, unknown>;
  /** Raw profile data returned from the provider API */
  rawProfile?: unknown;
};

/**
 * Fetches provider-specific user metadata based on the provider key
 */
async function fetchProviderMetadata(
  connectionId: string,
  providerKey: string
): Promise<ProviderMetadata | null> {
  const nango = new Nango({
    secretKey: process.env.NANGO_SECRET_KEY || '',
  });

  try {
    switch (providerKey) {
      case 'slack':
        return await fetchSlackMetadata(nango, connectionId);
      case 'gmail':
        return await fetchGmailMetadata(nango, connectionId);
      case 'google-calendar':
        return await fetchGoogleCalendarMetadata(nango, connectionId);
      case 'google-sheet':
        return await fetchGoogleSheetsMetadata(nango, connectionId);
      case 'x-social':
        return await fetchTwitterMetadata(nango, connectionId);
      case 'linkedin':
        return await fetchLinkedInMetadata(nango, connectionId);
      default:
        console.log(`No metadata fetcher available for provider: ${providerKey}`);
        return null;
    }
  } catch (error) {
    console.error(`Error fetching metadata for ${providerKey}:`, error);
    return null;
  }
}

/**
 * SLACK
 */
async function fetchSlackMetadata(_nango: Nango, connectionId: string): Promise<ProviderMetadata> {
  const response = await fetch(
    `https://api.nango.dev/connection/${connectionId}?provider_config_key=slack`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${process.env.NANGO_SECRET_KEY}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch Slack metadata: ${response.statusText}`);
  }

  const data = await response.json();
  const slackUserId = data?.credentials?.raw?.authed_user?.id;

  if (!slackUserId) {
    throw new Error('Could not extract Slack user ID from connection data');
  }

  const userInfoResponse = await fetch(
    `https://api.nango.dev/proxy/${connectionId}?provider_config_key=slack&endpoint=${encodeURIComponent(
      `/users.info?user=${slackUserId}`
    )}`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${process.env.NANGO_SECRET_KEY}`,
      },
    }
  );

  if (!userInfoResponse.ok) {
    const failedData = await userInfoResponse.json().catch(() => null);
    return {
      providerUserId: slackUserId,
      displayName: `Slack User (${slackUserId})`,
      additionalMetadata: {
        accountId: slackUserId,
      },
      rawProfile: failedData,
    };
  }

  const userInfo = await userInfoResponse.json();
  const displayName =
    userInfo?.result?.user?.real_name ||
    userInfo?.result?.user?.name ||
    `Slack User (${slackUserId})`;

  return {
    providerUserId: slackUserId,
    displayName,
    additionalMetadata: {
      accountId: slackUserId,
    },
    rawProfile: userInfo,
  };
}

/**
 * GMAIL
 */
async function fetchGmailMetadata(nango: Nango, connectionId: string): Promise<ProviderMetadata> {
  const response = await nango.proxy({
    connectionId,
    providerConfigKey: 'gmail',
    endpoint: 'https://www.googleapis.com/oauth2/v2/userinfo',
    method: 'GET',
  });

  if (response.status !== 200) {
    throw new Error(`Failed to fetch Gmail user info: ${response.statusText}`);
  }

  const data = response.data;
  const userId = data.id;
  const email = data.email;
  const name = data.name || email || userId;

  return {
    providerUserId: userId,
    displayName: email || name,
    additionalMetadata: {
      accountId: email || userId,
    },
    rawProfile: data,
  };
}

/**
 * GOOGLE CALENDAR
 */
async function fetchGoogleCalendarMetadata(
  nango: Nango,
  connectionId: string
): Promise<ProviderMetadata> {
  return await fetchGmailMetadata(nango, connectionId);
}

/**
 * GOOGLE SHEETS
 */
async function fetchGoogleSheetsMetadata(
  nango: Nango,
  connectionId: string
): Promise<ProviderMetadata> {
  return await fetchGmailMetadata(nango, connectionId);
}

/**
 * X
 */
type XUserProfile = {
  id: string;
  name: string;
  username: string;
  profile_image_url: string;
  description: string;
  location: string;
  url: string;
  protected: boolean;
  verified: boolean;
  followers_count: number;
  following_count: number;
  tweet_count: number;
  listed_count: number;
};

async function fetchTwitterMetadata(nango: Nango, connectionId: string): Promise<ProviderMetadata> {
  try {
    const result = await nango.triggerAction('x-social', connectionId, 'get-user-profile');

    const profile = result as unknown as XUserProfile;

    if (!profile || !profile.id) {
      throw new Error('Could not retrieve Twitter user profile');
    }

    const displayName = profile.username
      ? `@${profile.username}`
      : profile.name || `Twitter User`;

    return {
      providerUserId: profile.id,
      displayName,
      additionalMetadata: {
        accountId: profile.username || profile.id,
      },
      rawProfile: profile,
    };
  } catch (error) {
    console.error('Error fetching Twitter metadata:', error);
    throw error;
  }
}

/**
 * LINKEDIN
 */
type LinkedInUserProfile = {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: {
    displayImage: string;
  };
  vanityName?: string;
  headline?: string;
  publicProfileUrl?: string;
  email?: string;
  location?: {
    country?: {
      code?: string;
    };
  };
  industry?: string;
  connectionCount?: number;
};

async function fetchLinkedInMetadata(
  nango: Nango,
  connectionId: string
): Promise<ProviderMetadata> {
  try {
    const result = await nango.triggerAction('linkedin', connectionId, 'get-user-profile');

    const profile = result as unknown as LinkedInUserProfile;

    if (!profile || !profile.id) {
      throw new Error('Could not retrieve LinkedIn user profile');
    }

    const firstName = profile.firstName;
    const lastName = profile.lastName;
    const displayName = `${firstName} ${lastName}`.trim() || `LinkedIn User`;

    return {
      providerUserId: profile.id,
      displayName,
      additionalMetadata: {
        accountId: profile.vanityName ? profile.vanityName : undefined,
      },
      rawProfile: profile,
    };
  } catch (error) {
    console.error('Error fetching LinkedIn metadata:', error);
    throw error;
  }
}


export type { ProviderMetadata };
export { fetchProviderMetadata };
