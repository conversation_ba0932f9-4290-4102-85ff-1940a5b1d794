import { deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { checkRequiredConnections } from './checkRequiredConnections';
import { SupabaseClient } from '@supabase/supabase-js';

const mockSupabase = (connections: { providerKey: string; userId: string }[] = []) =>
  ({
    from: (table: string) => {
      if (table === 'connections') {
        return {
          select: () => ({
            eq: (k: string, v: string) => ({
              eq: (k2: string, v2: string) => ({
                maybeSingle: async () => {
                  const found = connections.find(
                    c => c[k as keyof typeof c] === v && c[k2 as keyof typeof c] === v2
                  );
                  return found
                    ? { data: { id: 'connection-id' }, error: null }
                    : { data: null, error: null };
                },
              }),
            }),
          }),
        };
      }
      return {};
    },
  }) as unknown as SupabaseClient;

test('should return an empty array if workflowSchema has no provider nodes', async () => {
  const workflowSchema = {
    nodes: [{ id: 'node1', type: 'ai.simple', parameters: {} }],
    triggers: [],
  };
  const missing = await checkRequiredConnections(workflowSchema as any, 'user', mockSupabase());
  deepStrictEqual(missing, []);
});

test('should return an empty array if user has all required connections', async () => {
  const workflowSchema = {
    nodes: [
      { id: 'node1', type: 'provider.google-mail.compose-draft', parameters: {} },
      { id: 'node2', type: 'provider.google-calendar.create-event', parameters: {} },
    ],
    triggers: [],
  };
  const conns = [
    { providerKey: 'google-mail', userId: 'user' },
    { providerKey: 'google-calendar', userId: 'user' },
  ];
  const missing = await checkRequiredConnections(
    workflowSchema as any,
    'user',
    mockSupabase(conns)
  );
  deepStrictEqual(missing, []);
});

test('should return missing providers if user lacks connections', async () => {
  const workflowSchema = {
    nodes: [
      { id: 'node1', type: 'provider.google-mail.compose-draft', parameters: {} },
      { id: 'node2', type: 'provider.dropbox.upload-file', parameters: {} },
    ],
    triggers: [],
  };
  const conns = [{ providerKey: 'google-mail', userId: 'user' }];
  const missing = await checkRequiredConnections(
    workflowSchema as any,
    'user',
    mockSupabase(conns)
  );
  deepStrictEqual(missing, ['dropbox']);
});

test('should return missing providers if Supabase query returns an error', async () => {
  const workflowSchema = {
    nodes: [{ id: 'node1', type: 'provider.google-mail.compose-draft', parameters: {} }],
    triggers: [],
  };
  const supabase = {
    from: () => ({
      select: () => ({
        eq: () => ({
          eq: () => ({
            maybeSingle: async () => ({ data: null, error: new Error('Supabase error') }),
          }),
        }),
      }),
    }),
  } as unknown as SupabaseClient;
  const missing = await checkRequiredConnections(workflowSchema as any, 'user', supabase);
  deepStrictEqual(missing, ['google-mail']);
});

test('should handle workflowSchema with triggers', async () => {
  const workflowSchema = {
    nodes: [{ id: 'node1', type: 'ai.simple', parameters: {} }],
    triggers: [
      { id: 't1', type: 'trigger.syncTrigger', parameters: { providerKey: 'google-mail' } },
    ],
  };
  const missing = await checkRequiredConnections(
    workflowSchema as any,
    'user',
    mockSupabase([{ providerKey: 'google-mail', userId: 'user' }])
  );
  deepStrictEqual(missing, []);
});
