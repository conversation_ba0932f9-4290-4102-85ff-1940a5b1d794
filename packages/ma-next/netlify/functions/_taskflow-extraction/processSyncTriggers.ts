import { SupabaseClient } from '@supabase/supabase-js';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';

/**
 * Creates entries in the sync_triggers table for the extracted trigger nodes
 */
async function createSyncTriggers(
  taskflowId: string,
  syncTriggerNodes: any[],
  supabase: SupabaseClient
): Promise<void> {
  const facade = new TaskflowSupabaseFacade(supabase);
  if (syncTriggerNodes.length === 0) {
    return;
  }

  // Create entries in the sync_triggers table
  const syncTriggerPromises = syncTriggerNodes.map(async (node: any) => {
    const { error } = await facade.createSyncTrigger({
      taskflowId,
      providerKey: node.parameters.providerKey,
      model: node.parameters.model,
      syncKey: node.parameters.syncKey,
      condition: node.condition || null,
    });

    if (error) {
      console.error('Error creating sync trigger:', error);
      throw error;
    }
  });

  await Promise.all(syncTriggerPromises);
}

export { createSyncTriggers };
