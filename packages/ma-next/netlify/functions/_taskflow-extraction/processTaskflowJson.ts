import { createAndTriggerExecution } from './createAndTriggerExecution';
import { getDebugMode } from '../_shared/debug';
import { TextProcessorContext } from './textProcessor';
import { checkRequiredConnections } from './checkRequiredConnections';
import { createTaskflow } from './createTaskflow';
import { RawTaskflowTransformer } from './RawTaskflowTransformer';

const DEBUG = getDebugMode();

/**
 * Process the taskflow JSON: create the taskflow and potentially trigger the execution.
 */
async function processTaskflowJson(
  jsonString: string,
  context: TextProcessorContext
): Promise<void> {
  const { chatProtocolEnqueuer, user, supabase, conversationId } = context;

  try {
    const schema = JSON.parse(jsonString);

    const [missingProviders, requiredProviders] = await checkRequiredConnections(
      schema,
      user.id,
      supabase
    );

    const transformer = new RawTaskflowTransformer(schema);
    transformer.addRequiredConnections(requiredProviders);

    const taskflow = await createTaskflow(
      transformer.getSchema(),
      conversationId,
      supabase
    );

    if (DEBUG) console.log(JSON.stringify(taskflow, null, 2));

    if (!taskflow) return;

    // Agent or task mode taskflow?
    if (taskflow.schema?.triggers?.length > 0) {
      const taskflowInfo = { id: taskflow.id };
      chatProtocolEnqueuer.addTaskflowChunk(taskflowInfo);
    } else {
      await createAndTriggerExecution({
        ...context,
        taskflowId: taskflow.id,
        triggerData: taskflow.schema.triggerData,
        setupSteps: missingProviders.map(provider => ({
          type: 'connectProvider',
          provider,
          completed: false,
        })),
      });
    }
  } catch (error) {
    console.error('Error parsing or processing taskflow JSON:', error);
    chatProtocolEnqueuer.addError('taskflow extraction failed');
  }
}

export { processTaskflowJson };
