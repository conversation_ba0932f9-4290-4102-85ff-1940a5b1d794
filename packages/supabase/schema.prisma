// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Profile {
  id            String          @id // Profiles should only be created via auth.users trigger
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @default(now()) @updatedAt
  firstName     String?
  lastName      String?
  preferences   Json? // User preferences like timeZone, etc.
  conversations Conversation[]
  Connection    Connection[]
  ConnectionLog ConnectionLog[]
  McpeasyServer McpeasyServer?

  @@map("profiles")
}

model Conversation {
  id                String     @id @default(uuid())
  title             String?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @default(now()) @updatedAt
  userId            String
  user              Profile    @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages          Json
  currentTaskflowId String?    @unique
  currentTaskflow   Taskflow?  @relation("currentTaskflow", fields: [currentTaskflowId], references: [id])
  taskflows         Taskflow[] @relation("taskflows")
  mode              Mode       @default(task)
  oaiResponseId     String?
  @@map("conversations")
}

enum Mode {
  task
  agent
}

model Taskflow {
  id                      String              @id @default(uuid())
  schema                  Json
  conversationId          String
  conversation            Conversation        @relation("taskflows", fields: [conversationId], references: [id], onDelete: Cascade)
  activeInConversation    Conversation?       @relation("currentTaskflow")
  executions              TaskflowExecution[] @relation("TaskflowToExecutions")
  syncTriggers            SyncTrigger[]
  testExecutionId         String?             @unique
  testExecution           TaskflowExecution?  @relation("TaskflowToTestExecution", fields: [testExecutionId], references: [id], onDelete: Cascade)
  active                  Boolean             @default(false)
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @default(now()) @updatedAt
  @@map("taskflows")
}

model TaskflowExecution {
  id              String    @id @default(uuid())
  taskflowId      String
  taskflow        Taskflow  @relation("TaskflowToExecutions", fields: [taskflowId], references: [id], onDelete: Cascade)
  triggerData     Json?
  context         Json      @default("{}") // Execution context/state
  result          Json?
  status          String? // Overall execution status (SUCCESS, ERROR, etc.)
  startedAt       DateTime  @default(now())
  updatedAt       DateTime  @default(now()) @updatedAt
  completedAt     DateTime?
  testForTaskflow Taskflow? @relation("TaskflowToTestExecution")

  @@index([taskflowId])
  @@map("taskflow_executions")
}

model SyncTrigger {
  id          String   @id @default(uuid())
  taskflowId  String
  taskflow    Taskflow @relation(fields: [taskflowId], references: [id], onDelete: Cascade)
  providerKey String
  model       String
  syncKey     String
  cursor      String?
  condition  Json?
  createdAt   DateTime @default(now())

  @@index([taskflowId])
  @@map("sync_triggers")
}

/**
 * Shared
 */
model Connection {
  id             String  @id
  providerKey    String
  userId         String
  user           Profile @relation(fields: [userId], references: [id], onDelete: Cascade)
  providerUserId String? // 3rd party system's id for this user.
  displayName    String? // User's display name from the provider
  metadata       Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("connections")
}

/**
 * EasyMCPeasy
 */
model ConnectionLog {
  id                String        @id @default(uuid())
  sessionId         String        @unique
  userId            String
  user              Profile       @relation(fields: [userId], references: [id], onDelete: Cascade)
  connectionTime    DateTime      @default(now())
  disconnectionTime DateTime?
  toolCalls         ToolCallLog[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([userId])
  @@index([connectionTime])
  @@map("emcpe_connection_log")
}

model ToolCallLog {
  id         String   @id @default(uuid())
  sessionId  String
  toolName   String
  timestamp  DateTime @default(now())
  statusCode Int

  connection ConnectionLog @relation(fields: [sessionId], references: [sessionId], onDelete: Cascade)

  @@index([sessionId])
  @@index([timestamp])
  @@map("emcpe_tool_call")
}

model McpeasyServer {
  id             String  @id @default(uuid())
  tokenEncrypted String
  tokenHash      String
  userId         String  @unique
  user           Profile @relation(fields: [userId], references: [id], onDelete: Cascade)
  preferences    Json? // { "enablements": { "providerKey_actionName": boolean } }

  @@map("emcpe_servers")
}
