/** @type {import('next').NextConfig} */
const nextConfig = {
  // Restrict to localhost only for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
  // Disable telemetry for privacy
  telemetry: false,
};

module.exports = nextConfig;