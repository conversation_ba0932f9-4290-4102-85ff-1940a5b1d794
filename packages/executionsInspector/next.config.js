/** @type {import('next').NextConfig} */
const nextConfig = {
  // Restrict to localhost only for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
  typescript: {
    // Ignore TypeScript errors during build for external packages
    ignoreBuildErrors: true,
  },
  eslint: {
    // Ignore ESLint errors during build
    ignoreDuringBuilds: true,
  },
  webpack: (config, { isServer }) => {
    // Only apply this configuration for server-side builds (API routes)
    if (isServer) {
      // Add support for TypeScript files in ma-next
      config.module.rules.push({
        test: /\.ts$/,
        include: [/ma-next/],
        use: [
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true,
              compilerOptions: {
                target: 'es2020',
                module: 'commonjs',
                moduleResolution: 'node',
                esModuleInterop: true,
                allowSyntheticDefaultImports: true,
                skipLibCheck: true,
              },
            },
          },
        ],
      });

      // Handle lodash-es imports
      config.resolve.alias = {
        ...config.resolve.alias,
        'lodash-es': 'lodash',
      };
    }
    return config;
  },
};

module.exports = nextConfig;
