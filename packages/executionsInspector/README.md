# Executions Inspector

A lightweight, localhost-only web application for inspecting and triggering taskflow executions in the MakeAgent system.

## Features

- **User Selection**: Filter taskflow executions by user ID (matches profiles table)
- **Taskflow Selection**: View available taskflows for selected user
- **Manual Trigger Mode**: Input JSON trigger data to execute taskflows
- **Execution Inspection Mode**: View detailed step-by-step execution results
- **Real-time Updates**: Live updates via Supabase Realtime subscriptions
- **Schema Display**: Persistent panel showing taskflow schema details

## Setup

1. Ensure environment variables are distributed:
   ```bash
   # From the root of the MakeAgent monorepo
   npm run distribute-env
   ```

2. Install dependencies:
   ```bash
   cd packages/executionsInspector
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open http://localhost:3001 in your browser

## Environment Variables

Required environment variables (loaded via distributeEnv.ts):
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

## Usage

1. **Select a User**: Choose from the dropdown of available users (from profiles table)
2. **Select a Taskflow**: Choose a taskflow belonging to the selected user
3. **Choose Mode**:
   - **Manual Trigger**: Enter JSON trigger data and execute the taskflow
   - **Execution Inspection**: Select an existing execution to view detailed results
4. **View Results**: Monitor execution progress in real-time

## Constraints

- **Localhost Only**: This tool is designed for local development and debugging only
- **Read-Only Database**: No database modifications except through the executeTaskflow API
- **No Production Deployment**: Not intended for production use

## Technical Details

- Built with Next.js and React
- Uses @supabase/supabase-js for database queries
- Styled with Tailwind CSS
- Real-time updates via Supabase Realtime
- Integrates with existing MakeAgent taskflow execution engine