import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Profile, Conversation, Taskflow, TaskflowExecution } from '@/types';
import { UserSelector } from '@/components/UserSelector';
import { ConversationSelector } from '@/components/ConversationSelector';
import { TaskflowSelector } from '@/components/TaskflowSelector';
import { ExecutionSelector } from '@/components/ExecutionSelector';
import { SchemaPanel } from '@/components/SchemaPanel';
import { ManualTriggerMode } from '@/components/ManualTriggerMode';
import { ExecutionDetails } from '@/components/ExecutionDetails';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, Activity } from 'lucide-react';

export default function Home() {
  const [selectedUser, setSelectedUser] = useState<Profile | null>(null);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [selectedTaskflow, setSelectedTaskflow] = useState<Taskflow | null>(null);
  const [executions, setExecutions] = useState<TaskflowExecution[]>([]);
  const [selectedExecution, setSelectedExecution] = useState<TaskflowExecution | null>(null);
  const [mode, setMode] = useState<'trigger' | 'inspection'>('trigger');
  const [isLocalhost, setIsLocalhost] = useState(true);

  // Only allow localhost
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      setIsLocalhost(isLocal);
    }
  }, []);

  // Fetch latest execution on mount and set up the hierarchy
  useEffect(() => {
    async function init() {
      const { data: last } = await supabase
        .from('taskflow_executions')
        .select('id, taskflowId')
        .order('updatedAt', { ascending: false })
        .limit(1)
        .maybeSingle();
      if (!last) return;

      const { data: taskflow } = await supabase
        .from('taskflows')
        .select('*, conversations!taskflows_conversationId_fkey(userId)')
        .eq('id', last.taskflowId)
        .maybeSingle();
      if (!taskflow) return;

      const userId = (taskflow as any).conversations?.userId;
      if (userId) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('id, firstName, lastName, preferences, createdAt, updatedAt')
          .eq('id', userId)
          .maybeSingle();
        if (profile) setSelectedUser(profile as Profile);

        const { data: conversation } = await supabase
          .from('conversations')
          .select('*')
          .eq('id', taskflow.conversationId)
          .maybeSingle();
        if (conversation) setSelectedConversation(conversation as Conversation);
      }

      setSelectedTaskflow(taskflow as Taskflow);
      const { data: exec } = await supabase
        .from('taskflow_executions')
        .select('*')
        .eq('id', last.id)
        .maybeSingle();
      if (exec) {
        setMode('inspection');
        setSelectedExecution(exec as TaskflowExecution);
      }
    }
    init();
  }, []);

  // Reset conversation when user changes manually
  useEffect(() => {
    setSelectedConversation(null);
    setSelectedTaskflow(null);
    setExecutions([]);
    setSelectedExecution(null);
    setMode('trigger');
  }, [selectedUser?.id]);

  // Reset taskflow when conversation changes manually
  useEffect(() => {
    setSelectedTaskflow(null);
    setExecutions([]);
    setSelectedExecution(null);
    setMode('trigger');
  }, [selectedConversation?.id]);

  // Fetch executions for selected taskflow
  useEffect(() => {
    if (!selectedTaskflow) return;
    async function fetchExecutions() {
      const { data } = await supabase
        .from('taskflow_executions')
        .select('*')
        .eq('taskflowId', selectedTaskflow!.id)
        .order('updatedAt', { ascending: false })
        .limit(50);
      setExecutions(data || []);
    }
    fetchExecutions();
  }, [selectedTaskflow?.id]);

  if (!isLocalhost) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="flex flex-col items-center text-center py-8">
            <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
            <h1 className="text-xl font-semibold mb-2">Access Restricted</h1>
            <p className="text-muted-foreground">This application is designed to run on localhost only for security reasons.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleTriggerMode = () => {
    setMode('trigger');
    setSelectedExecution(null);
  };

  const handleExecutionSelect = (execution: TaskflowExecution | null) => {
    if (execution) {
      setMode('inspection');
      setSelectedExecution(execution);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="text-center space-y-2">
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Taskflow Executions Inspector</span>
          </h1>
          <p className="text-muted-foreground">Debug and trigger taskflow executions locally</p>
        </header>

        {/* Navigation Bar with Selection Dropdowns */}
        <Card>
          <CardContent className="py-4">
            <div className="flex flex-wrap gap-4 items-end justify-center">
              <UserSelector selectedUser={selectedUser} onUserSelect={setSelectedUser} />

              {selectedUser && (
                <>
                  <Separator orientation="vertical" className="h-8" />
                  <ConversationSelector
                    userId={selectedUser.id}
                    selectedConversation={selectedConversation}
                    onConversationSelect={setSelectedConversation}
                  />
                </>
              )}

              {selectedConversation && (
                <>
                  <Separator orientation="vertical" className="h-8" />
                  <TaskflowSelector
                    conversationId={selectedConversation.id}
                    selectedTaskflow={selectedTaskflow}
                    onTaskflowSelect={setSelectedTaskflow}
                  />
                </>
              )}

              {selectedTaskflow && (
                <>
                  <Separator orientation="vertical" className="h-8" />
                  <ExecutionSelector
                    executions={executions}
                    selectedExecution={selectedExecution}
                    onExecutionSelect={handleExecutionSelect}
                    onTriggerMode={handleTriggerMode}
                  />
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Area */}
        {selectedTaskflow ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Schema Information */}
            <div className="space-y-4">
              <SchemaPanel taskflow={selectedTaskflow} />
            </div>

            {/* Right Column - Execution Details or Manual Trigger */}
            <div className="space-y-4">
              {mode === 'trigger' && selectedUser && (
                <ManualTriggerMode taskflow={selectedTaskflow} userId={selectedUser.id} />
              )}
              {mode === 'inspection' && selectedExecution && (
                <ExecutionDetails executionId={selectedExecution.id} />
              )}
            </div>
          </div>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12 text-center">
              <Activity className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Select a Taskflow</h3>
              <p className="text-muted-foreground">
                Choose a user, conversation, and taskflow from the navigation above to get started.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
