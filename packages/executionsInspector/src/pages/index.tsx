import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Profile, Taskflow, TaskflowExecution } from '@/types';
import { UserSelector } from '@/components/UserSelector';
import { TaskflowSelector } from '@/components/TaskflowSelector';
import { SchemaPanel } from '@/components/SchemaPanel';
import { ManualTriggerMode } from '@/components/ManualTriggerMode';
import { ExecutionDetails } from '@/components/ExecutionDetails';
import { AlertTriangle } from 'lucide-react';

export default function Home() {
  const [selectedUser, setSelectedUser] = useState<Profile | null>(null);
  const [selectedTaskflow, setSelectedTaskflow] = useState<Taskflow | null>(null);
  const [executions, setExecutions] = useState<TaskflowExecution[]>([]);
  const [selectedExecution, setSelectedExecution] = useState<TaskflowExecution | null>(null);
  const [mode, setMode] = useState<'trigger' | 'inspection'>('trigger');
  const [isLocalhost, setIsLocalhost] = useState(true);

  // Only allow localhost
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      setIsLocalhost(isLocal);
    }
  }, []);

  // Fetch latest execution on mount
  useEffect(() => {
    async function init() {
      const { data: last } = await supabase
        .from('taskflow_executions')
        .select('id, taskflowId')
        .order('updatedAt', { ascending: false })
        .limit(1)
        .maybeSingle();
      if (!last) return;

      const { data: taskflow } = await supabase
        .from('taskflows')
        .select('*, conversations!taskflows_conversationId_fkey(userId)')
        .eq('id', last.taskflowId)
        .maybeSingle();
      if (!taskflow) return;

      const userId = (taskflow as any).conversations?.userId;
      if (userId) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('id, firstName, lastName, preferences, createdAt, updatedAt')
          .eq('id', userId)
          .maybeSingle();
        if (profile) setSelectedUser(profile as Profile);
      }

      setSelectedTaskflow(taskflow as Taskflow);
      const { data: exec } = await supabase
        .from('taskflow_executions')
        .select('*')
        .eq('id', last.id)
        .maybeSingle();
      if (exec) {
        setMode('inspection');
        setSelectedExecution(exec as TaskflowExecution);
      }
    }
    init();
  }, []);

  // Reset taskflow when user changes manually
  useEffect(() => {
    setSelectedTaskflow(null);
    setExecutions([]);
    setSelectedExecution(null);
    setMode('trigger');
  }, [selectedUser?.id]);

  // Fetch executions for selected taskflow
  useEffect(() => {
    if (!selectedTaskflow) return;
    async function fetchExecutions() {
      const { data } = await supabase
        .from('taskflow_executions')
        .select('*')
        .eq('taskflowId', selectedTaskflow.id)
        .order('updatedAt', { ascending: false })
        .limit(50);
      setExecutions(data || []);
    }
    fetchExecutions();
  }, [selectedTaskflow?.id]);

  if (!isLocalhost) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="card max-w-md w-full text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h1 className="text-xl font-semibold mb-2">Access Restricted</h1>
          <p className="text-muted-foreground">This application is designed to run on localhost only for security reasons.</p>
        </div>
      </div>
    );
  }

  const handleModeChange = (value: string) => {
    if (value === 'trigger') {
      setMode('trigger');
      setSelectedExecution(null);
    } else {
      const exec = executions.find(e => e.id === value);
      if (exec) {
        setMode('inspection');
        setSelectedExecution(exec);
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        <header>
          <h1 className="text-3xl font-bold mb-2">Taskflow Executions Inspector</h1>
          <p className="text-muted-foreground">Debug and trigger taskflow executions locally</p>
        </header>

        <div className="flex flex-wrap gap-4 items-end">
          <UserSelector selectedUser={selectedUser} onUserSelect={setSelectedUser} />
          {selectedUser && (
            <TaskflowSelector userId={selectedUser.id} selectedTaskflow={selectedTaskflow} onTaskflowSelect={setSelectedTaskflow} />
          )}
          {selectedTaskflow && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Mode / Execution</label>
              <select
                className="select"
                value={mode === 'trigger' ? 'trigger' : selectedExecution?.id || ''}
                onChange={e => handleModeChange(e.target.value)}
              >
                <option value="trigger">Trigger New Execution</option>
                {executions.map(exec => (
                  <option key={exec.id} value={exec.id}>
                    {new Date(exec.updatedAt).toLocaleString()} - {exec.status || 'UNKNOWN'}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        {selectedTaskflow && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              {mode === 'trigger' && selectedUser && <ManualTriggerMode taskflow={selectedTaskflow} userId={selectedUser.id} />}
              {mode === 'inspection' && selectedExecution && <ExecutionDetails executionId={selectedExecution.id} />}
            </div>
            <div className="lg:col-span-1">
              <SchemaPanel taskflow={selectedTaskflow} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
