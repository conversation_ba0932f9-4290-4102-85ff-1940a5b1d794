import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

// Import the executeTaskFlow function from ma-next
import { executeTaskFlow } from '../../../../ma-next/netlify/functions/_taskflow/executeTaskFlow';
import { TaskflowSupabaseFacade } from '../../../../ma-next/netlify/functions/_taskflow/taskflowSupabaseFacade';
import { DebugTracer } from '../../../../ma-next/netlify/functions/_taskflow/debugTracer';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Only allow localhost
  const host = req.headers.host;
  if (!host || (!host.includes('localhost') && !host.includes('127.0.0.1'))) {
    return res.status(403).json({ error: 'Access denied' });
  }

  try {
    const { taskflowId, triggerData, userId } = req.body;

    if (!taskflowId || !triggerData || !userId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const supabaseFacade = new TaskflowSupabaseFacade(supabase as any);
    const tracer = new DebugTracer(supabaseFacade);

    // Execute the taskflow using the ma-next engine
    const [result, error] = await executeTaskFlow({
      taskflowId,
      triggerData,
      supabaseFacade,
      userId,
      tracer,
    });

    if (error) {
      console.error('Error executing taskflow:', error);
      return res.status(500).json({ error: error.message });
    }

    return res.status(200).json({ success: true, result });
  } catch (error) {
    console.error('Error in execute API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
