import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

interface TaskflowExecution {
  id: string;
  taskflowId: string;
  context: Record<string, any>;
  triggerData: any;
  result: any;
  status: string;
  startedAt: string;
  completedAt: string | null;
  updatedAt: string;
}

interface ExecutionDetailsProps {
  executionId: string;
}

export function ExecutionDetails({ executionId }: ExecutionDetailsProps) {
  const [execution, setExecution] = useState<TaskflowExecution | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // fetch execution
  useEffect(() => {
    async function fetchExecution() {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from('taskflow_executions')
          .select('*')
          .eq('id', executionId)
          .maybeSingle();
        if (error) throw error;
        if (data) setExecution(data as TaskflowExecution);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch execution');
      } finally {
        setLoading(false);
      }
    }
    if (executionId) {
      fetchExecution();
    }
  }, [executionId]);

  // realtime updates
  useEffect(() => {
    if (!executionId) return;
    const channel = supabase
      .channel(`execution-${executionId}`)
      .on(
        'postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'taskflow_executions', filter: `id=eq.${executionId}` },
        payload => {
          if (payload.new) setExecution(payload.new as TaskflowExecution);
        }
      )
      .subscribe();
    return () => {
      channel.unsubscribe();
    };
  }, [executionId]);

  if (loading) {
    return <div className="text-center py-4">Loading execution...</div>;
  }

  if (error || !execution) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
        <p className="text-red-700">{error || 'Execution not found'}</p>
      </div>
    );
  }

  const renderStep = (nodeId: string, nodeData: any) => {
    const status = nodeData.status || 'UNKNOWN';
    const statusColor = {
      SUCCESS: 'text-green-600',
      RUNNING: 'text-blue-600',
      PAUSED: 'text-yellow-600',
      ERROR: 'text-red-600',
      UNKNOWN: 'text-gray-600',
    }[status] || 'text-gray-600';

    return (
      <div key={nodeId} className="border rounded-md p-4 space-y-2">
        <div className="flex items-center space-x-3">
          <span className={`font-medium ${statusColor}`}>{status}</span>
          <span className="font-mono text-sm">{nodeId}</span>
          {nodeData.type && <span className="text-sm text-muted-foreground">({nodeData.type})</span>}
        </div>
        {nodeData.output && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-1">Output:</h4>
            <pre className="text-xs bg-white p-2 border rounded overflow-auto max-h-40">
              {JSON.stringify(nodeData.output, null, 2)}
            </pre>
          </div>
        )}
        {nodeData.error && (
          <div>
            <h4 className="text-sm font-medium text-red-700 mb-1">Error:</h4>
            <pre className="text-xs bg-red-50 p-2 border border-red-200 rounded overflow-auto max-h-40">
              {typeof nodeData.error === 'string' ? nodeData.error : JSON.stringify(nodeData.error, null, 2)}
            </pre>
          </div>
        )}
        {nodeData.hitl && (
          <div>
            <h4 className="text-sm font-medium text-blue-700 mb-1">Human in the Loop:</h4>
            <pre className="text-xs bg-blue-50 p-2 border border-blue-200 rounded overflow-auto max-h-40">
              {JSON.stringify(nodeData.hitl, null, 2)}
            </pre>
          </div>
        )}
        {nodeData.steps && Array.isArray(nodeData.steps) && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-1">Steps:</h4>
            <div className="space-y-1">
              {nodeData.steps.map((step: any, idx: number) => (
                <div key={idx} className="text-xs bg-white p-2 border rounded">
                  <span className={`font-medium ${step.completed ? 'text-green-600' : 'text-gray-600'}`}>{step.completed ? '✓' : '○'}</span>
                  <span className="ml-2">
                    {step.type} {step.provider && `(${step.provider})`}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const stepsEntries = execution.context && typeof execution.context === 'object' ? Object.entries(execution.context) : [];

  return (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-md">
        <h3 className="font-medium text-gray-900 mb-2">Execution Details</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">ID:</span>
            <span className="ml-2 font-mono">{execution.id}</span>
          </div>
          <div>
            <span className="text-gray-600">Status:</span>
            <span className={`ml-2 font-medium ${
              execution.status === 'SUCCESS'
                ? 'text-green-600'
                : execution.status === 'ERROR'
                ? 'text-red-600'
                : execution.status === 'RUNNING'
                ? 'text-blue-600'
                : execution.status === 'PAUSED'
                ? 'text-yellow-600'
                : 'text-gray-600'
            }`}>{execution.status}</span>
          </div>
          <div>
            <span className="text-gray-600">Started:</span>
            <span className="ml-2">{new Date(execution.startedAt).toLocaleString()}</span>
          </div>
          <div>
            <span className="text-gray-600">Completed:</span>
            <span className="ml-2">{execution.completedAt ? new Date(execution.completedAt).toLocaleString() : 'Not completed'}</span>
          </div>
        </div>
        {execution.triggerData && (
          <div className="mt-3">
            <span className="text-gray-600 text-sm">Trigger Data:</span>
            <pre className="text-xs bg-white p-2 border rounded mt-1 overflow-auto max-h-20">
              {JSON.stringify(execution.triggerData, null, 2)}
            </pre>
          </div>
        )}
      </div>

      <div>
        <h3 className="font-medium text-gray-900 mb-3">Execution Steps</h3>
        {stepsEntries.length === 0 ? (
          <div className="text-gray-500 text-sm">No execution steps available</div>
        ) : (
          <div className="space-y-2">
            {stepsEntries.map(([nodeId, nodeData]) => renderStep(nodeId, nodeData))}
          </div>
        )}
      </div>
    </div>
  );
}
