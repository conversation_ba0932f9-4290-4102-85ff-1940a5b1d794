import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Conversation } from '@/types';
import { MessageSquare } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ConversationSelectorProps {
  userId: string;
  selectedConversation: Conversation | null;
  onConversationSelect: (conversation: Conversation | null) => void;
}

export function ConversationSelector({
  userId,
  selectedConversation,
  onConversationSelect
}: ConversationSelectorProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (userId) {
      fetchConversations();
    } else {
      setConversations([]);
    }
  }, [userId]);

  const fetchConversations = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('conversations')
        .select('id, title, userId, createdAt, updatedAt')
        .eq('userId', userId)
        .order('updatedAt', { ascending: false });

      if (error) throw error;

      setConversations(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch conversations');
      console.error('Error fetching conversations:', err);
    } finally {
      setLoading(false);
    }
  };

  const getConversationDisplayName = (conversation: Conversation) => {
    if (conversation.title) {
      return conversation.title;
    }
    return `Conversation ${conversation.id.substring(0, 8)}...`;
  };

  const handleConversationSelect = (conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    onConversationSelect(conversation || null);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">Conversation</label>
        <Select disabled>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Loading..." />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">Conversation</label>
        <Select disabled>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Error loading" />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-muted-foreground">Conversation</label>
      <Select
        value={selectedConversation?.id || ''}
        onValueChange={handleConversationSelect}
      >
        <SelectTrigger className="w-[200px]">
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
            <SelectValue placeholder="Select conversation..." />
          </div>
        </SelectTrigger>
        <SelectContent>
          {conversations.length === 0 ? (
            <div className="p-3 text-sm text-muted-foreground">
              No conversations found
            </div>
          ) : (
            conversations.map((conversation) => (
              <SelectItem key={conversation.id} value={conversation.id}>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {getConversationDisplayName(conversation)}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    Updated: {new Date(conversation.updatedAt).toLocaleDateString()}
                  </span>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
