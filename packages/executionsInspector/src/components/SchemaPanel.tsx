import { Taskflow } from '@/types';
import { Code } from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/cjs/styles/prism';

interface SchemaPanelProps {
  taskflow: Taskflow;
}

export function SchemaPanel({ taskflow }: SchemaPanelProps) {
  const { schema } = taskflow;

  return (
    <div className="card sticky top-6 space-y-4">
      <div className="flex items-center space-x-2">
        <Code className="h-5 w-5" />
        <h3 className="text-lg font-semibold">Schema</h3>
      </div>

      <div className="space-y-6 text-sm">
        <div className="space-y-1">
          <h4 className="font-medium">Basic Info</h4>
          <div className="space-y-1 ml-2">
            <div>ID: {taskflow.id}</div>
            <div>Active: {taskflow.active ? 'Yes' : 'No'}</div>
            <div>Created: {new Date(taskflow.createdAt).toLocaleString()}</div>
            {schema?.name && <div>Name: {schema.name}</div>}
            {schema?.description && <div>Description: {schema.description}</div>}
          </div>
        </div>

        {schema?.triggers && (
          <div className="space-y-1">
            <h4 className="font-medium">Triggers ({schema.triggers.length})</h4>
            <SyntaxHighlighter
              language="json"
              style={tomorrow}
              customStyle={{ margin: 0, fontSize: '12px', backgroundColor: 'transparent' }}
            >
              {JSON.stringify(schema.triggers, null, 2)}
            </SyntaxHighlighter>
          </div>
        )}

        {schema?.nodes && (
          <div className="space-y-1">
            <h4 className="font-medium">Nodes ({schema.nodes.length})</h4>
            <SyntaxHighlighter
              language="json"
              style={tomorrow}
              customStyle={{ margin: 0, fontSize: '12px', backgroundColor: 'transparent' }}
            >
              {JSON.stringify(schema.nodes, null, 2)}
            </SyntaxHighlighter>
          </div>
        )}

        <div className="space-y-1">
          <h4 className="font-medium">Full Schema</h4>
          <div className="max-h-96 overflow-auto">
            <SyntaxHighlighter
              language="json"
              style={tomorrow}
              customStyle={{ margin: 0, fontSize: '12px', backgroundColor: 'transparent' }}
            >
              {JSON.stringify(schema, null, 2)}
            </SyntaxHighlighter>
          </div>
        </div>
      </div>
    </div>
  );
}
