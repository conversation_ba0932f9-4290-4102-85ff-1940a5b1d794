import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Taskflow } from '@/types';
import { Workflow } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface TaskflowSelectorProps {
  conversationId: string;
  selectedTaskflow: Taskflow | null;
  onTaskflowSelect: (taskflow: Taskflow | null) => void;
}

export function TaskflowSelector({ conversationId, selectedTaskflow, onTaskflowSelect }: TaskflowSelectorProps) {
  const [taskflows, setTaskflows] = useState<Taskflow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (conversationId) {
      fetchTaskflows();
    } else {
      setTaskflows([]);
    }
  }, [conversationId]);

  const fetchTaskflows = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('taskflows')
        .select(`
          id,
          schema,
          conversationId,
          active,
          createdAt,
          updatedAt,
          testExecutionId
        `)
        .eq('conversationId', conversationId)
        .order('createdAt', { ascending: false });

      if (error) throw error;

      setTaskflows(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch taskflows');
      console.error('Error fetching taskflows:', err);
    } finally {
      setLoading(false);
    }
  };

  const getTaskflowDisplayName = (taskflow: Taskflow) => {
    const schema = taskflow.schema;
    if (schema?.name) {
      return schema.name;
    }
    if (schema?.title) {
      return schema.title;
    }
    return `Taskflow ${taskflow.id.substring(0, 8)}...`;
  };

  const getTaskflowDescription = (taskflow: Taskflow) => {
    const schema = taskflow.schema;
    if (schema?.description) {
      return schema.description;
    }
    const nodeCount = schema?.nodes?.length || 0;
    const triggerCount = schema?.triggers?.length || 0;
    return `${nodeCount} nodes, ${triggerCount} triggers`;
  };

  const handleTaskflowSelect = (taskflowId: string) => {
    const taskflow = taskflows.find(t => t.id === taskflowId);
    onTaskflowSelect(taskflow || null);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">Taskflow</label>
        <Select disabled>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Loading..." />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">Taskflow</label>
        <Select disabled>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Error loading" />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-muted-foreground">Taskflow</label>
      <Select
        value={selectedTaskflow?.id || ''}
        onValueChange={handleTaskflowSelect}
      >
        <SelectTrigger className="w-[200px]">
          <div className="flex items-center space-x-2">
            <Workflow className="h-4 w-4 text-muted-foreground" />
            <SelectValue placeholder="Select taskflow..." />
          </div>
        </SelectTrigger>
        <SelectContent>
          {taskflows.length === 0 ? (
            <SelectItem value="" disabled>
              No taskflows found
            </SelectItem>
          ) : (
            taskflows.map((taskflow) => (
              <SelectItem key={taskflow.id} value={taskflow.id}>
                <div className="flex flex-col">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">
                      {getTaskflowDisplayName(taskflow)}
                    </span>
                    {taskflow.active && (
                      <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                        Active
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {getTaskflowDescription(taskflow)}
                  </span>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
