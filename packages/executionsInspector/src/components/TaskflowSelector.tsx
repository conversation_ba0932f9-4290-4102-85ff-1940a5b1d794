import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Taskflow } from '@/types';
import { ChevronDown, Workflow } from 'lucide-react';

interface TaskflowSelectorProps {
  userId: string;
  selectedTaskflow: Taskflow | null;
  onTaskflowSelect: (taskflow: Taskflow | null) => void;
}

export function TaskflowSelector({ userId, selectedTaskflow, onTaskflowSelect }: TaskflowSelectorProps) {
  const [taskflows, setTaskflows] = useState<Taskflow[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (userId) {
      fetchTaskflows();
    } else {
      setTaskflows([]);
    }
  }, [userId]);

  const fetchTaskflows = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('taskflows')
        .select(`
          id,
          schema,
          conversationId,
          active,
          createdAt,
          updatedAt,
          testExecutionId,
          conversations!taskflows_conversationId_fkey!inner(userId)
        `)
        .eq('conversations.userId', userId)
        .order('createdAt', { ascending: false });

      if (error) throw error;

      setTaskflows(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch taskflows');
      console.error('Error fetching taskflows:', err);
    } finally {
      setLoading(false);
    }
  };

  const getTaskflowDisplayName = (taskflow: Taskflow) => {
    const schema = taskflow.schema;
    if (schema?.name) {
      return schema.name;
    }
    if (schema?.title) {
      return schema.title;
    }
    return `Taskflow ${taskflow.id.substring(0, 8)}...`;
  };

  const getTaskflowDescription = (taskflow: Taskflow) => {
    const schema = taskflow.schema;
    if (schema?.description) {
      return schema.description;
    }
    const nodeCount = schema?.nodes?.length || 0;
    const triggerCount = schema?.triggers?.length || 0;
    return `${nodeCount} nodes, ${triggerCount} triggers`;
  };

  const handleTaskflowSelect = (taskflow: Taskflow) => {
    onTaskflowSelect(taskflow);
    setIsOpen(false);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">Select Taskflow</label>
        <div className="select opacity-50">
          Loading taskflows...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">Select Taskflow</label>
        <div className="p-3 border border-destructive rounded-md text-destructive text-sm">
          Error: {error}
        </div>
        <button
          onClick={fetchTaskflows}
          className="text-sm text-muted-foreground hover:text-foreground"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium">Select Taskflow</label>
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="select w-full text-left"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Workflow className="h-4 w-4 text-muted-foreground" />
              <span>
                {selectedTaskflow
                  ? getTaskflowDisplayName(selectedTaskflow)
                  : 'Choose a taskflow...'
                }
              </span>
            </div>
            <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-background border rounded-md shadow-lg max-h-60 overflow-auto">
            {taskflows.length === 0 ? (
              <div className="p-3 text-sm text-muted-foreground">
                No taskflows found for this user
              </div>
            ) : (
              taskflows.map((taskflow) => (
                <button
                  key={taskflow.id}
                  onClick={() => handleTaskflowSelect(taskflow)}
                  className="w-full p-3 text-left hover:bg-muted transition-colors border-b last:border-b-0"
                >
                  <div className="flex items-center space-x-3">
                    <Workflow className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">
                          {getTaskflowDisplayName(taskflow)}
                        </span>
                        {taskflow.active && (
                          <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">
                            Active
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {getTaskflowDescription(taskflow)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        ID: {taskflow.id.substring(0, 8)}...
                      </div>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
