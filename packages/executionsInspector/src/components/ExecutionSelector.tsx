import { TaskflowExecution } from '@/types';
import { Play, CheckCircle, XCircle, Clock, Pause } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ExecutionSelectorProps {
  executions: TaskflowExecution[];
  selectedExecution: TaskflowExecution | null;
  onExecutionSelect: (execution: TaskflowExecution | null) => void;
  onTriggerMode: () => void;
}

const STATUS_ICONS = {
  SUCCESS: CheckCircle,
  ERROR: XCircle,
  RUNNING: Play,
  PAUSED: Pause,
  PENDING: Clock,
} as const;

const STATUS_COLORS = {
  SUCCESS: 'text-green-600',
  ERROR: 'text-red-600',
  RUNNING: 'text-blue-600',
  PAUSED: 'text-yellow-600',
  PENDING: 'text-gray-600',
} as const;

export function ExecutionSelector({ 
  executions, 
  selectedExecution, 
  onExecutionSelect,
  onTriggerMode
}: ExecutionSelectorProps) {
  const handleExecutionSelect = (value: string) => {
    if (value === 'trigger') {
      onTriggerMode();
    } else {
      const execution = executions.find(e => e.id === value);
      onExecutionSelect(execution || null);
    }
  };

  const getExecutionDisplayName = (execution: TaskflowExecution) => {
    const status = execution.status || 'UNKNOWN';
    const date = new Date(execution.updatedAt).toLocaleString();
    return `${status} - ${date}`;
  };

  const getStatusIcon = (status: string) => {
    const IconComponent = STATUS_ICONS[status as keyof typeof STATUS_ICONS] || Clock;
    const colorClass = STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'text-gray-600';
    return <IconComponent className={`h-4 w-4 ${colorClass}`} />;
  };

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-muted-foreground">Mode / Execution</label>
      <Select
        value={selectedExecution?.id || 'trigger'}
        onValueChange={handleExecutionSelect}
      >
        <SelectTrigger className="w-[250px]">
          <SelectValue placeholder="Select mode..." />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="trigger">
            <div className="flex items-center space-x-2">
              <Play className="h-4 w-4 text-blue-600" />
              <span>Trigger New Execution</span>
            </div>
          </SelectItem>
          {executions.length > 0 && (
            <>
              <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground">
                Recent Executions
              </div>
              {executions.slice(0, 10).map((execution) => (
                <SelectItem key={execution.id} value={execution.id}>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(execution.status || 'UNKNOWN')}
                    <div className="flex flex-col">
                      <span className="font-medium">
                        {execution.status || 'UNKNOWN'}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(execution.updatedAt).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </>
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
