import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Profile } from '@/types';
import { User } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface UserSelectorProps {
  selectedUser: Profile | null;
  onUserSelect: (user: Profile | null) => void;
}

export function UserSelector({ selectedUser, onUserSelect }: UserSelectorProps) {
  const [users, setUsers] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, firstName, lastName, preferences, createdAt, updatedAt')
        .order('createdAt', { ascending: false });

      if (error) throw error;

      setUsers(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const getUserDisplayName = (user: Profile) => {
    if (user.firstName || user.lastName) {
      return `${user.firstName || ''} ${user.lastName || ''}`.trim();
    }
    return user.id.substring(0, 8) + '...';
  };

  const handleUserSelect = (userId: string) => {
    const user = users.find(u => u.id === userId);
    onUserSelect(user || null);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">User</label>
        <Select disabled>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Loading..." />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">User</label>
        <Select disabled>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Error loading" />
          </SelectTrigger>
        </Select>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-muted-foreground">User</label>
      <Select
        value={selectedUser?.id || ''}
        onValueChange={handleUserSelect}
      >
        <SelectTrigger className="w-[180px]">
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <SelectValue placeholder="Select user..." />
          </div>
        </SelectTrigger>
        <SelectContent>
          {users.length === 0 ? (
            <div className="p-3 text-sm text-muted-foreground">
              No users found
            </div>
          ) : (
            users.map((user) => (
              <SelectItem key={user.id} value={user.id}>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {getUserDisplayName(user)}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    ID: {user.id.substring(0, 8)}...
                  </span>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
