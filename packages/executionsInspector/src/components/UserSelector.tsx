import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Profile } from '@/types';
import { ChevronDown, User } from 'lucide-react';

interface UserSelectorProps {
  selectedUser: Profile | null;
  onUserSelect: (user: Profile | null) => void;
}

export function UserSelector({ selectedUser, onUserSelect }: UserSelectorProps) {
  const [users, setUsers] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, firstName, lastName, preferences, createdAt, updatedAt')
        .order('createdAt', { ascending: false });

      if (error) throw error;
      
      setUsers(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const getUserDisplayName = (user: Profile) => {
    if (user.firstName || user.lastName) {
      return `${user.firstName || ''} ${user.lastName || ''}`.trim();
    }
    return user.id.substring(0, 8) + '...';
  };

  const handleUserSelect = (user: Profile) => {
    onUserSelect(user);
    setIsOpen(false);
  };

  if (loading) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">Select User</label>
        <div className="select opacity-50">
          Loading users...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">Select User</label>
        <div className="p-3 border border-destructive rounded-md text-destructive text-sm">
          Error: {error}
        </div>
        <button 
          onClick={fetchUsers}
          className="text-sm text-muted-foreground hover:text-foreground"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium">Select User</label>
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="select w-full text-left"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span>
                {selectedUser 
                  ? getUserDisplayName(selectedUser)
                  : 'Choose a user...'
                }
              </span>
            </div>
            <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-background border rounded-md shadow-lg max-h-60 overflow-auto">
            {users.length === 0 ? (
              <div className="p-3 text-sm text-muted-foreground">
                No users found
              </div>
            ) : (
              users.map((user) => (
                <button
                  key={user.id}
                  onClick={() => handleUserSelect(user)}
                  className="w-full p-3 text-left hover:bg-muted transition-colors border-b last:border-b-0"
                >
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">
                        {getUserDisplayName(user)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        ID: {user.id.substring(0, 8)}...
                      </div>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}