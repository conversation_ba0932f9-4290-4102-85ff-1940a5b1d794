import { useState } from 'react';

interface ManualTriggerModeProps {
  taskflow: {
    id: string;
    name: string;
    schema: any;
  };
  userId: string;
}

export function ManualTriggerMode({ taskflow, userId }: ManualTriggerModeProps) {
  const [triggerData, setTriggerData] = useState('{}');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleExecute = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Validate JSON
      let parsedTriggerData;
      try {
        parsedTriggerData = JSON.parse(triggerData);
      } catch (parseError) {
        throw new Error('Invalid JSON format');
      }

      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskflowId: taskflow.id,
          triggerData: parsedTriggerData,
          userId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const executionResult = await response.json();
      setResult(executionResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute taskflow');
    } finally {
      setLoading(false);
    }
  };

  const isValidJson = () => {
    try {
      JSON.parse(triggerData);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Trigger Data (JSON)
        </label>
        <textarea
          value={triggerData}
          onChange={(e) => setTriggerData(e.target.value)}
          className={`w-full h-32 p-3 border rounded-md font-mono text-sm ${
            isValidJson() ? 'border-gray-300' : 'border-red-300'
          }`}
          placeholder='{"key": "value"}'
        />
        {!isValidJson() && (
          <p className="text-red-500 text-sm mt-1">Invalid JSON format</p>
        )}
      </div>

      <button
        onClick={handleExecute}
        disabled={loading || !isValidJson()}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? 'Executing...' : 'Execute Taskflow'}
      </button>

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <h3 className="text-red-800 font-medium">Error</h3>
          <p className="text-red-700 text-sm mt-1">{error}</p>
        </div>
      )}

      {result && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md">
          <h3 className="text-green-800 font-medium">Execution Started</h3>
          <div className="text-green-700 text-sm mt-2">
            <p><strong>Execution ID:</strong> {result.executionId}</p>
            {result.result && (
              <div className="mt-2">
                <strong>Result:</strong>
                <pre className="bg-white p-2 border rounded text-xs mt-1 overflow-auto">
                  {JSON.stringify(result.result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}