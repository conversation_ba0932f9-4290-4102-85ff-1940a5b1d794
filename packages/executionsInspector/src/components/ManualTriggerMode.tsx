import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Play, CheckCircle, AlertCircle, Activity } from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/cjs/styles/prism';

interface ManualTriggerModeProps {
  taskflow: {
    id: string;
    schema: any;
  };
  userId: string;
}

export function ManualTriggerMode({ taskflow, userId }: ManualTriggerModeProps) {
  const [triggerData, setTriggerData] = useState('{}');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleExecute = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Validate JSON
      let parsedTriggerData;
      try {
        parsedTriggerData = JSON.parse(triggerData);
      } catch (parseError) {
        throw new Error('Invalid JSON format');
      }

      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskflowId: taskflow.id,
          triggerData: parsedTriggerData,
          userId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const executionResult = await response.json();
      setResult(executionResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute taskflow');
    } finally {
      setLoading(false);
    }
  };

  const isValidJson = () => {
    try {
      JSON.parse(triggerData);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Play className="h-5 w-5" />
            <span>Manual Trigger</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Trigger Data (JSON)
            </label>
            <textarea
              value={triggerData}
              onChange={(e) => setTriggerData(e.target.value)}
              className={`w-full h-32 p-3 border rounded-md font-mono text-sm resize-none ${
                isValidJson()
                  ? 'border-input focus:ring-2 focus:ring-ring focus:border-transparent'
                  : 'border-red-300 focus:ring-2 focus:ring-red-500'
              }`}
              placeholder='{"key": "value"}'
            />
            {!isValidJson() && (
              <p className="text-red-500 text-sm mt-1 flex items-center space-x-1">
                <AlertCircle className="h-3 w-3" />
                <span>Invalid JSON format</span>
              </p>
            )}
          </div>

          <Button
            onClick={handleExecute}
            disabled={loading || !isValidJson()}
            className="w-full"
          >
            {loading ? (
              <>
                <Activity className="h-4 w-4 mr-2 animate-spin" />
                Executing...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Execute Taskflow
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {error && (
        <Card className="border-red-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span>Error</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700 text-sm">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <Card className="border-green-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <span>Execution Started</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <span className="text-sm font-medium">Execution ID:</span>
              <div className="font-mono text-xs mt-1 p-2 bg-muted rounded">
                {result.executionId}
              </div>
            </div>
            {result.result && (
              <>
                <Separator />
                <div>
                  <span className="text-sm font-medium">Result:</span>
                  <div className="mt-2 max-h-32 overflow-auto">
                    <SyntaxHighlighter
                      language="json"
                      style={tomorrow}
                      customStyle={{
                        margin: 0,
                        fontSize: '11px',
                        backgroundColor: 'transparent',
                        padding: '8px'
                      }}
                    >
                      {JSON.stringify(result.result, null, 2)}
                    </SyntaxHighlighter>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
