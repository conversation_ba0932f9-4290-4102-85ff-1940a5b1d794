{"name": "@makeagent/executions-inspector", "version": "1.0.0", "description": "Localhost-only web application for inspecting and triggering taskflow executions", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@supabase/supabase-js": "^2.45.0", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-syntax-highlighter": "^15.5.0", "lucide-react": "^0.300.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-syntax-highlighter": "^15.5.0", "autoprefixer": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "typescript": "^5.0.0"}}