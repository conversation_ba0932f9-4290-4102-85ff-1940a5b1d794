integrations:
    google-mail:
        syncs:
            emails-fork:
                runs: every 1 minutes
                description: |
                    Fetches a list of emails from Gmail. Defaults to 1-day backfill,
                    adjustable via `backfillPeriodMs` in milliseconds.
                input: OptionalBackfillSetting
                version: 1.0.4
                scopes:
                    - https://www.googleapis.com/auth/gmail.readonly
                output: GmailEmail
                sync_type: incremental
                endpoint:
                    method: GET
                    path: /emails-fork
                    group: Emails
            emails-labels-added:  # New sync for labels added
                runs: every 1 minutes
                description: |
                    Tracks emails where labels have been added using Gmail History API.
                    Stores historyId in metadata for incremental syncing.
                version: 1.0.0
                scopes:
                    - https://www.googleapis.com/auth/gmail.readonly
                output: GmailEmailLabelTriggered
                sync_type: incremental
                endpoint:
                    method: GET
                    path: /labels-added
                    group: Emails
    x-social:
        syncs:
            user-tweets:
                auto_start: true
                runs: every 15 minutes
                description: |
                    Fetches tweets from a user's timeline. Initially fetches the last 10 tweets,
                    then uses since_id and until_id to track new tweets incrementally.
                input: TwitterUserInput
                version: 1.0.0
                scopes:
                    - tweet.read
                    - users.read
                output: TwitterPost
                sync_type: incremental
                endpoint:
                    method: GET
                    path: /user-tweets
                    group: Tweets
            user-mentions:
                auto_start: true
                runs: every 15 minutes
                description: |
                    Fetches tweets that mention the authenticated user. Initially fetches the last 10 mentions,
                    then uses since_id to track new mentions incrementally.
                version: 1.0.0
                scopes:
                    - tweet.read
                    - users.read
                output: TwitterMention
                sync_type: incremental
                endpoint:
                    method: GET
                    path: /user-mentions
                    group: Tweets

models:
    GmailEmail:
        id: string
        sender: string
        recipients: string
        date: string
        subject: string
        body: string
        attachments: Attachments[]
        threadId: string
        isDraft: boolean
        labels: string[]
        snippet: string
        cc: string
        bcc: string
        messageId: string
        inReplyTo: string
        references: string
    GmailEmailLabelTriggered:
        id: string
        sender: string
        recipients: string
        date: string
        subject: string
        body: string
        attachments: Attachments[]
        threadId: string
        isDraft: boolean
        labels: string[]
        snippet: string
        cc: string
        bcc: string
        messageId: string
        inReplyTo: string
        references: string
    Attachments:
        filename: string
        mimeType: string
        size: number
        attachmentId: string
    OptionalBackfillSetting:
        backfillPeriodMs?: number
    TwitterPostInput:
        text: string            # The text content of the post
        reply_to?: string       # Optional ID of a post to reply to
        quote?: string          # Optional ID of a post to quote
    XSocialPostOutput:
        id: string              # The ID of the created post
        text: string            # The text of the post
        created_at: string      # When the post was created
    TwitterUserInput:
        username: string        # Twitter username to fetch tweets for
    TwitterPost:
        id: string              # Tweet ID
        text: string            # Tweet text content
        created_at: string      # When the tweet was created
        author_id: string       # ID of the user who created the tweet
        conversation_id: string # ID of the conversation/thread
        in_reply_to_user_id?: string  # ID of the user being replied to, if applicable
        referenced_tweets?: ReferencedTweet[]  # Tweets referenced by this tweet (e.g., retweets, quotes)
        public_metrics: TweetMetrics  # Public engagement metrics
    ReferencedTweet:
        type: string            # Type of reference (replied_to, quoted, retweeted)
        id: string              # ID of the referenced tweet
    TweetMetrics:
        retweet_count: number   # Number of retweets
        reply_count: number     # Number of replies
        like_count: number      # Number of likes
        quote_count: number     # Number of quote tweets
    TwitterMention:
        id: string              # Tweet ID
        text: string            # Tweet text content
        created_at: string      # When the tweet was created
        author_id: string       # ID of the user who created the tweet
        conversation_id: string # ID of the conversation/thread
        in_reply_to_user_id?: string  # ID of the user being replied to, if applicable
        referenced_tweets?: ReferencedTweet[]  # Tweets referenced by this tweet (e.g., retweets, quotes)
        public_metrics: TweetMetrics  # Public engagement metrics
        mention: Mention        # Mention details
    Mention:
        username: string        # Username mentioned
        id: string              # ID of the user mentioned
    XSocialUserProfile:
        id: string              # User ID
        name: string            # User name
        username: string        # User username
        profile_image_url: string  # URL of the user's profile image
        description: string     # User description
        location: string        # User location
        url: string             # User URL
        protected: boolean      # Whether the user's tweets are protected
        verified: boolean       # Whether the user is verified
        followers_count: number # Number of followers
        following_count: number # Number of users being followed
        tweet_count: number     # Number of tweets
        listed_count: number    # Number of lists the user is a member of

