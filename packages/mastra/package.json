{"name": "@makeagent/mastra", "version": "0.1.0", "description": "Package for agent orchestration", "type": "module", "main": "dist/index.js", "scripts": {"build": "mastra build", "dev": "<PERSON>ra dev", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ai-sdk/anthropic": "latest", "@ai-sdk/google": "latest", "@ai-sdk/openai": "latest", "@mastra/core": "latest", "@mastra/deployer-netlify": "latest", "@mastra/libsql": "latest", "@mastra/memory": "latest", "@nangohq/node": "^0.58.6", "@supabase/supabase-js": "latest", "mastra": "latest", "zod": "latest"}, "devDependencies": {"@types/node": "latest", "tsx": "latest", "typescript": "latest"}}